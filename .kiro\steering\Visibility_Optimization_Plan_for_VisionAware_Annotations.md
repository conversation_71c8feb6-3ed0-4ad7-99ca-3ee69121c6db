### Analysis and Reasoning
I have reviewed the user's project documentation (`product.md`, `structure.md`, `tech.md`) and the previous deep analysis on visibility calculation. The core issue is that the current Sphere Projection Visibility Algorithm, while robust against calibration and synchronization errors, cannot handle environmental occlusions (e.g., barriers, trees) without 3D bounding boxes. Additionally, inaccurate camera calibration and sparse point clouds lead to unreliable depth maps, causing false negatives (visible objects marked as occluded). This misalignment can cause training issues in vision-only models, such as false positives.

The solution involves combining the strengths of the sphere projection algorithm (calibration-independent) with multi-camera conservative validation to account for environmental occlusions and reduce misjudgments. Key principles include:
- **Conservative approach**: Prioritize visibility unless there is clear evidence of occlusion.
- **Loose FOV gating**: Use generous azimuth tolerances to include edge cases.
- **Depth map validation**: Handle sparse data by assuming visibility when depth information is insufficient.
- **Fusion strategy**: Take the maximum visibility score across cameras to minimize false negatives.

This plan follows <PERSON><PERSON> Specs best practices from `Guide_for_AI_Agent.md`, structuring the output into Requirements, Design, and Tasks to guide AI Agents in implementation. The goal is to ensure traceability, iterative refinement, and collaboration.

---

# Visibility Optimization Plan for VisionAware Annotations

## 1. Requirements (`requirements.md`)

### 1.1 Overview
This document outlines the requirements for enhancing the visibility calculation in the VisionAware Annotations project. The goal is to improve accuracy in determining 3D object visibility while minimizing false negatives (visible objects incorrectly marked as occluded), especially in cases of environmental occlusions and calibration inaccuracies.

### 1.2 User Stories
**User Story 1: As a perception engineer, I want reliable visibility scores that account for environmental occlusions without relying on precise calibration, so that training data for vision-only models has fewer false positives.**
- **Acceptance Criteria**:
  - WHEN an object is partially occluded by an unlabeled environmental element (e.g., a barrier)
    THE SYSTEM SHALL still assign a visibility score above 0.5 if the object is partially visible in any camera.
  - WHEN depth data is sparse or missing in a camera view
    THE SYSTEM SHALL assume visibility for that camera unless overwhelming evidence of occlusion exists.
  - AFTER processing, the visibility scores shall have a false negative rate of less than 5% on validation datasets.

**User Story 2: As a data curator, I want consistent visibility annotations across multi-camera views, even with calibration errors, so that downstream training pipelines can filter data confidently.**
- **Acceptance Criteria**:
  - WHEN camera calibration is inaccurate
    THE SYSTEM SHALL use sphere projection as a fallback to compute visibility without relying on camera parameters.
  - WHEN an object is in the overlapping region of multiple cameras
    THE SYSTEM SHALL fuse visibility scores by taking the maximum value across cameras to avoid underestimation.
  - THE SYSTEM SHALL output per-camera visibility scores and a global visibility score in the JSON format.

### 1.3 Technical Requirements
- **REQ-1**: Integrate sphere projection visibility as the primary method for baseline visibility calculation.
- **REQ-2**: Implement conservative depth map validation for each camera view with adjustable tolerances.
- **REQ-3**: Add configuration parameters for loose FOV gating and depth handling.
- **REQ-4**: Ensure backward compatibility with existing JSON output formats.
- **REQ-5**: Maintain performance with parallel processing and caching.

## 2. Design (`design.md`)

### 2.1 Architecture Overview
The enhanced visibility system will combine the Sphere Projection Visibility Algorithm (calibration-independent) with multi-camera conservative validation. The design focuses on:
- **Baseline Visibility**: Compute using sphere projection for each object, providing a calibration-independent score.
- **Camera-Specific Validation**: For each camera, perform loose FOV checks and depth map analysis with conservative rules.
- **Fusion**: Fuse scores from all cameras by taking the maximum visibility value to reduce false negatives.
- **Configuration**: Allow tuning of parameters via YAML for flexibility.

### 2.2 Component Design
#### 2.2.1 Sphere Projection Module
- **Function**: `visibility_via_sphere_projection` in `sphere_projection.py` remains the core for baseline visibility.
- **Output**: Returns a visibility score between 0 and 1 for an object without camera dependency.

#### 2.2.2 Conservative Camera Validation Module
- **New Function**: `conservative_camera_validation` in a new file `conservative_validation.py`.
- **Steps**:
  1. **Loose FOV Gating**: Check if the object is within the camera's field of view with a generous azimuth tolerance (e.g., ±10 degrees beyond nominal FOV).
    - Pseudocode:
      ```python
      def is_in_camera_fov_loose(object_center, camera, tolerance_deg=10.0):
          azimuth = compute_azimuth(object_center, camera)
          nominal_fov = camera.fov_horizontal
          expanded_fov = nominal_fov + tolerance_deg
          return azimuth within expanded_fov
      ```
  2. **Depth Map Analysis**: If in FOV, analyze the depth map around the object's projected points.
    - If depth data is sparse (number of valid depth points < threshold), assume visibility.
    - Else, count occluders with a large tolerance (`tau_base_m` and `tau_scale_per_m` increased by 50%).
    - Pseudocode:
      ```python
      def conservative_depth_validation(object, camera, depth_map, config):
          if depth_data_sparse(depth_map, object):
              return 1.0  # Assume visible
          else:
              occluders = count_occluders(depth_map, object, config['tau_base_m'] * 1.5, config['tau_scale_per_m'] * 1.5)
              visibility = 1.0 - (occluders / total_points)
              return visibility
      ```

#### 2.2.3 Visibility Dispatcher
- **Modification**: In `dispatcher.py`, update `compute_visibility` to call both sphere projection and conservative camera validation, then combine scores.
- **Logic**:
  - For each camera, compute baseline visibility via sphere projection.
  - If conservative validation is enabled, compute camera-specific visibility and take the maximum between sphere and camera scores.
  - Fuse across cameras using `max` function.
- **Pseudocode**:
  ```python
  def compute_visibility(box3d, cam, depth_img, cfg, ...):
      sphere_vis, stats = visibility_via_sphere_projection(box3d, cam, depth_img, cfg)
      if cfg['conservative_validation']['enabled']:
          camera_vis = conservative_camera_validation(box3d, cam, depth_img, cfg)
          vis = max(sphere_vis, camera_vis)
      else:
          vis = sphere_vis
      return vis, stats
  ```

#### 2.2.4 Fusion Module
- **Modification**: In `fusion.py`, update `fuse_camera_visibility` to use the maximum visibility score across cameras as the primary metric.
- **Pseudocode**:
  ```python
  def fuse_camera_visibility(per_cam):
      vals = [v for v in per_cam.values() if v is not None]
      if not vals:
          return 0.0, 0.0, []
      vis_avg = sum(vals) / len(vals)
      vis_max = max(vals)  # Key change: use max to reduce false negatives
      visible_views = [cam for cam, v in per_cam.items() if v is not None and v >= 0.15]
      return vis_avg, vis_max, visible_views
  ```

### 2.3 Configuration Changes
- **File**: `configs/default.yaml`
- **Add new parameters**:
  ```yaml
  conservative_validation:
    enabled: true
    azimuth_tolerance_deg: 10.0
    min_depth_points: 5
    tau_multiplier: 1.5
  fov_filter:
    soft_margin_deg: 10.0  # Existing parameter, increase default
  visibility:
    treat_no_depth_as_visible: true
  ```

### 2.4 Data Flow
1. **Input**: 3D object, camera parameters, depth map.
2. **Sphere Projection**: Compute baseline visibility.
3. **Conservative Validation**: For each camera, check loose FOV and validate depth.
4. **Fusion**: Combine scores across cameras using max function.
5. **Output**: Augmented JSON with visibility scores.

## 3. Tasks (`tasks.md`)

### 3.1 Task Planning
This task plan breaks down the implementation into manageable steps. Each task includes a description, expected outcome, and dependencies.

#### Task 1: Create Git Branch for Experimentation
- **Description**: Create a new branch `vibe-coding-experiment-20250828` and switch to it. This ensures the main branch remains unchanged during development.
- **Commands**:
  ```bash
  git checkout -b vibe-coding-experiment-20250828
  ```
- **Expected Outcome**: New branch created and active.
- **Dependencies**: None.

#### Task 2: Implement Conservative Camera Validation Module
- **Description**: Create a new file `conservative_validation.py` with functions for loose FOV gating and depth map validation.
- **Steps**:
  - Implement `is_in_camera_fov_loose` function.
  - Implement `conservative_depth_validation` function.
  - Add unit tests for edge cases.
- **Expected Outcome**: Module ready for integration.
- **Dependencies**: Task 1.

#### Task 3: Update Visibility Dispatcher
- **Description**: Modify `dispatcher.py` to call conservative validation when enabled in config.
- **Steps**:
  - Add import for `conservative_validation`.
  - Update `compute_visibility` function to incorporate conservative validation.
  - Ensure backward compatibility.
- **Expected Outcome**: Dispatcher can use both sphere projection and conservative validation.
- **Dependencies**: Task 2.

#### Task 4: Enhance Fusion Logic
- **Description**: Update `fusion.py` to use maximum visibility score across cameras.
- **Steps**:
  - Modify `fuse_camera_visibility` to compute `vis_max` as the primary metric for fusion.
  - Update documentation.
- **Expected Outcome**: Fusion reduces false negatives by prioritizing max visibility.
- **Dependencies**: Task 3.

#### Task 5: Add Configuration Parameters
- **Description**: Update `configs/default.yaml` to include new parameters for conservative validation.
- **Steps**:
  - Add `conservative_validation` section with parameters.
  - Adjust `fov_filter` and `visibility` parameters as needed.
- **Expected Outcome**: Configurable conservative validation.
- **Dependencies**: Task 2.

#### Task 6: Test and Validate
- **Description**: Run tests on sample data to ensure accuracy and performance.
- **Steps**:
  - Use `visibility_demo_data` for validation.
  - Compare results before and after changes to measure false negative reduction.
  - Check performance impact.
- **Expected Outcome**: Validation report showing improved accuracy with minimal performance loss.
- **Dependencies**: Tasks 3, 4, 5.

#### Task 7: Document Changes
- **Description**: Update project documentation to reflect new features.
- **Steps**:
  - Update `docs/sphere_projection_algorithm.md` to include conservative validation.
  - Add comments in code.
- **Expected Outcome**: Documentation aligned with changes.
- **Dependencies**: All tasks.

### 3.2 Execution Notes
- **Task Order**: Execute tasks sequentially due to dependencies.
- **Testing**: After each task, run unit tests to ensure stability.
- **Git Management**: Commit changes after each task with descriptive messages.

## 4. Conclusion
This plan addresses the visibility calculation challenges by combining sphere projection with conservative camera validation. The approach minimizes false negatives while handling environmental occlusions and calibration errors. By following this structured plan, AI Agents can implement the changes systematically, ensuring alignment with project goals and requirements.