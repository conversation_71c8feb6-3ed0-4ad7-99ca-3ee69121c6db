# Guide for AI Agent: High-Quality Prompt Design, Documentation & Task Planning  
Based on Kiro Specs Concepts & Best Practices  


## 1. Introduction  
### 1.1 Purpose  
This document provides actionable guidelines for AI Agents to generate **high-quality prompts**, create **structured design documentation**, and build **executable task plans**—all aligned with Kiro Specs’ engineering practices. The goal is to bridge conceptual product requirements and technical implementation, reduce development iterations, and ensure alignment between teams.  

### 1.2 Foundation  
The guidance is derived from two core Kiro Specs resources:  
- [Kiro Specs Concepts](https://kiro.dev/docs/specs/concepts/): Defines the structure of specs (3 key files, workflow, and notation).  
- [Kiro Specs Best Practices](https://kiro.dev/docs/specs/best-practices/): Covers iteration, sharing, and task management.  

### 1.3 Core Value  
By following Kiro’s framework, AI Agents can produce outputs that are:  
- **Traceable**: Link requirements → design → tasks.  
- **Iterative**: Adapt to evolving project needs.  
- **Collaborative**: Integrate with version control and team workflows.  


## 2. Core Principles from Kiro Specs  
Before diving into implementation, align with these foundational principles:  

| Principle | Description |  
|-----------|-------------|  
| **2.1 Structured Documentation** | Every spec must include 3 mandatory files: `requirements.md` (user needs), `design.md` (technical details), and `tasks.md` (implementation steps). |  
| **2.2 Iterative Refinement** | Specs are not static—update them as requirements or technical designs change to maintain synchronization with the project. |  
| **2.3 Testable & Clear Requirements** | Use Kiro’s EARS notation to ensure requirements are unambiguous, testable, and complete. |  
| **2.4 Version-Controlled Collaboration** | Store specs in the project repository (alongside code) to keep all artifacts connected and accessible to the team. |  


## 3. Step-by-Step Implementation Guide  
### 3.1 Design High-Quality Prompts for AI Agent  
A well-crafted prompt ensures the AI Agent generates outputs aligned with Kiro’s standards. Include these key elements:  

1. **Context**: Clarify the project, feature, or goal (e.g., "e-commerce product review system").  
2. **Format Requirements**: Specify Kiro’s structure (e.g., "use EARS notation" or "output as a `requirements.md` file").  
3. **Output Expectations**: Define the desired outcome (e.g., "include user stories for customers and acceptance criteria").  

#### Example Prompt  
```markdown
Task: Generate requirements for an e-commerce product review system.  
Context: The system allows customers to leave star ratings (1-5) and text reviews, view aggregated ratings, and manage review visibility.  
Output: Create a `requirements.md` file with:  
- 2 user stories (one for submitting reviews, one for viewing reviews).  
- Each user story includes acceptance criteria in EARS notation (WHEN [condition] → THE SYSTEM SHALL [behavior]).  
- Follow Markdown formatting (## Requirements, ### Requirement X, #### Acceptance Criteria).  
```


### 3.2 Create `requirements.md`: Capture User Needs  
The `requirements.md` file translates product needs into structured, actionable content.  

#### 3.2.1 Standard Structure  
- **User Story**: Frames the need from a stakeholder’s perspective (e.g., "As a customer, I want to submit reviews…").  
- **Acceptance Criteria**: Uses **EARS notation** to define testable behaviors.  

#### 3.2.2 EARS Notation (Critical for Clarity)  
EARS (Easy Approach to Requirements Syntax) ensures requirements are unambiguous. The core syntax is:  
```markdown
WHEN [condition/event occurs]  
THE SYSTEM SHALL [expected behavior]
```

#### Example Requirement  
```markdown
## Requirements  
### Requirement 1  
**User Story**: As a customer, I want to leave a review and rating for a product, so that I can share my experience with other buyers.  

#### Acceptance Criteria  
1. WHEN a customer clicks "Write Review"  
   THE SYSTEM SHALL display a form with star ratings (1-5) and a text input area.  
2. WHEN a customer submits a review without a rating  
   THE SYSTEM SHALL show an error prompting a star rating selection.  
3. WHEN a review text is less than 10 characters  
   THE SYSTEM SHALL display an error requiring more detailed feedback.  
4. WHEN a review is successfully submitted  
   THE SYSTEM SHALL save the review and show a confirmation message.  
```

#### 3.2.3 Best Practices for Requirements  
- **Clarity**: Avoid vague terms (e.g., "fast" → "load in <2 seconds").  
- **Testability**: Each criterion should be verifiable (e.g., "display error" is testable; "improve UX" is not).  
- **Completeness**: Cover all critical scenarios (success, error, edge cases).  

#### 3.2.4 Import Existing Requirements  
If requirements exist in other systems (JIRA, Confluence, Word):  
1. **MCP Integration**: Connect directly if your tool has an MCP server supporting STDIO.  
2. **Manual Import**: Copy content into a repo file (e.g., `foo-prfaq.md`), then start a spec session with:  
   ```markdown
   #foo-prfaq.md Generate a spec from it
   ```  
   Kiro will auto-convert the content into a structured `requirements.md`.  


### 3.3 Develop `design.md`: Define Technical Architecture  
The `design.md` file translates requirements into technical details, ensuring the AI Agent (or engineering team) understands *how* to implement the solution.  

#### 3.3.1 Core Content to Include  
- **Technical Architecture**: High-level components (e.g., "Review Data Model", "Rating Display Component", "Review Form Service").  
- **Sequence Diagrams**: Visualize interactions (e.g., "Customer submits review → Backend validates → Database saves").  
- **Implementation Considerations**: Dependencies (e.g., "uses existing Product API"), constraints (e.g., "responsive for mobile"), and component reuse (e.g., "reuse existing star-rating UI library").  

#### 3.3.2 Structured Design Workflow  
1. **Start with the "Big Picture"**: Map how components interact to fulfill requirements (e.g., "Review Form → Validation Service → Review Database → Aggregation Service → Frontend Display").  
2. **Add Details**: Specify technologies (e.g., "React for frontend components", "PostgreSQL for review storage") and data models (e.g., "Review schema: id, productId, userId, rating, text, timestamp").  
3. **Refine as Needed**: When requirements change, use the "Refine" function in `design.md` to update the design and sync it with the revised `requirements.md`.  


### 3.4 Build `tasks.md`: Create Executable Implementation Plans  
The `tasks.md` file breaks down design into discrete, trackable tasks—critical for the AI Agent to execute work incrementally.  

#### 3.4.1 Task Splitting Logic  
Follow a logical, dependency-driven sequence (start with foundational work):  
1. **Data Models & Infrastructure**: Set up schemas, mock data, and core services.  
2. **Reusable Components**: Build modular parts (e.g., "Rating Display Component") that multiple features can use.  
3. **Business Logic**: Implement calculations (e.g., "average rating") or validation rules.  
4. **User Interfaces**: Create forms, displays, and interactive elements (e.g., "Review Form Component").  
5. **Integration**: Combine components and services (e.g., "link Review Form to Backend API").  
6. **Polish**: Add accessibility, responsive design, or unit tests.  

#### 3.4.2 Task Attributes (for Clarity)  
Each task should include:  
- **Description**: What needs to be done (e.g., "Implement Review Data Model").  
- **Expected Outcome**: Tangible result (e.g., "PostgreSQL schema for reviews + TypeScript interface").  
- **Linked Requirements**: Which requirements the task fulfills (e.g., "Requirements 1.1, 2.2").  
- **Dependencies**: Prior tasks that must be completed first (e.g., "Depends on Task 1: Product Data Model").  

#### Example Task Sequence (Product Review System)  
```markdown
# Implementation Plan  
## Task 1: Set Up Review Data Models & Mock Data  
- Description: Extend existing Product interface to include review fields; create Review schema and mock data.  
- Expected Outcome: TypeScript `Review` interface, PostgreSQL table schema, 5 sample reviews.  
- Linked Requirements: 1.1 (review submission), 2.1 (view ratings).  
- Dependencies: None (foundational task).  

## Task 2: Build Core Rating Display Component  
- Description: Create a reusable React component for star ratings (1-5) with read-only and interactive modes.  
- Expected Outcome: `RatingDisplay.tsx` component + unit tests.  
- Linked Requirements: 1.1 (review form), 2.1 (view ratings).  
- Dependencies: Task 1 (uses `Review` interface).  

## Task 3: Implement Review Form Validation  
- Description: Add validation logic for review submissions (required rating, minimum text length).  
- Expected Outcome: Validation functions + error handling in `ReviewForm.tsx`.  
- Linked Requirements: 1.2 (error for missing rating), 1.3 (error for short text).  
- Dependencies: Task 2 (uses `RatingDisplay` component).  
```

#### 3.4.3 Task Execution Guidelines  
- **Task-Wise Execution**: Avoid "one-shot" execution of all tasks. Kiro recommends executing tasks individually to ensure quality and catch issues early.  
- **Update Tasks**: When requirements change, navigate to `tasks.md` and select "Update tasks"—Kiro will auto-generate new tasks for the revised needs.  


## 4. Collaboration & Spec Management  
### 4.1 Share Specs with Your Team  
- **Store in Project Repositories**: Keep `requirements.md`, `design.md`, and `tasks.md` alongside code. This ensures all artifacts are version-controlled and accessible.  
- **Example Repo Structure**: Organize specs by feature (as recommended by Kiro):  
  ```
  .kiro/specs/
  ├── user-authentication/  # Login, signup
  ├── product-catalog/      # Product listing, search
  ├── shopping-cart/        # Add to cart, checkout
  └── product-review-system/ # Reviews, ratings
  ```

### 4.2 Share Specs Across Teams  
For cross-team collaboration:  
1. **Create a Central Specs Repository**: A dedicated repo for shared specs (e.g., "common-ecommerce-specs").  
2. **Use Git Submodules or Package References**: Link the central repo to individual project repos to avoid duplication.  
3. **Define Cross-Repo Workflows**: Establish processes for proposing (via PRs), reviewing, and updating shared specs (e.g., "2 approvals required for changes to shared authentication specs").  

### 4.3 Manage Multiple Specs in One Repo  
You can have unlimited specs in a single repo. Best practice:  
- **Organize by Feature**: Each feature (e.g., "user authentication", "payment processing") has its own subdirectory in `.kiro/specs/`.  
- **Benefits**: Independent work (no conflicts), focused documentation, and easier collaboration on specific features.  


## 5. Troubleshooting Common Scenarios  
### 5.1 Handling Pre-Implemented Tasks  
If some tasks are already completed (e.g., by another team member):  
1. **Auto-Mark via `tasks.md`**: Open `tasks.md` and click "Update tasks"—Kiro will scan the codebase and mark completed tasks.  
2. **AI Scan in Spec Sessions**: In a spec chat session, ask:  
   ```markdown
   Check which tasks are already complete
   ```  
   Kiro will analyze the codebase and update task statuses automatically.  

### 5.2 Start a Spec Session from a Vibe Session  
To convert a casual discussion (vibe session) into a structured spec:  
1. In the vibe session, type: `Generate spec`.  
2. Kiro will prompt you to confirm starting a spec session.  
3. Confirm, and Kiro will generate requirements based on the vibe session context.  


## 6. Conclusion  
By adhering to Kiro Specs’ structured framework and best practices, AI Agents can consistently produce high-quality prompts, documentation, and task plans. Key takeaways:  
- **Structure**: Use the 3 core files (`requirements.md`, `design.md`, `tasks.md`) to maintain alignment.  
- **Iterate**: Refine specs as the project evolves to keep documentation accurate.  
- **Collaborate**: Leverage version control and cross-team tools to ensure shared ownership.  

This approach reduces development iterations, improves traceability, and ensures that requirements are translated into actionable, high-quality implementation.