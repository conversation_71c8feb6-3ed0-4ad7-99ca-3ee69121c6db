# ROADMAP.md - Project Development Blueprint

## Project Overview
This document provides a comprehensive roadmap of the current project status, including core architecture, implementation details, and development plans. It serves as the primary reference for AI Agents to understand and interact with the project effectively.

# VisionAware Annotations - Project Roadmap

## Executive Summary

The VisionAware Annotations project addresses a critical challenge in autonomous driving perception: the transition from expensive multi-modal sensor systems (cameras + LiDAR) to cost-effective vision-only solutions. Our toolkit automatically calculates visibility and occlusion attributes for 3D annotated objects, solving the "supervision signal mismatch" problem that causes false-positive detections in vision-only models.


## Project Background
- Objective: Provide robust, configuration-driven visibility/occlusion annotations for multi-camera pure-vision autonomous driving datasets (RoboBUS), enabling downstream training/data curation with reliable occlusion levels and visibility statistics.
- Current Version: Unversioned (working branch) — initial public toolkit iteration
- Development Stage: Phase 4 optimization — parallel per-object processing and improved visualization/diagnostics integrated; Z-buffer era modules removed
- Core Technologies: Python (>=3.10), NumPy, OpenCV, Open3D, Matplotlib, PyYAML, Numba, TQDM; multi-threaded CPU pipeline (ThreadPoolExecutor)

## Core Tasks in Progress
1. T2 - Safe config access for include_noFOV_as_zero to avoid KeyError in batch pipeline (90%)
2. End-to-end batch pipeline stabilization with per-camera depth caching and parallel object processing (85%)
3. Visualization overlays and occlusion-level gating across cameras with mosaics (70%)

## Algorithm Implementation
### Core Algorithm Principles
- Algorithm Name: Sphere Projection Visibility
- Mathematical Basis: Project dense samples of 3D bounding-box surfaces into each camera image; form circular neighborhoods in image space around projected samples; compare depth map values to expected surface depths with distance-aware tolerance; compute occlusion fraction and aggregate to a per-camera visibility rate and fused visibility statistics across cameras.
- Optimization Approach: Per-frame, per-camera depth maps generated once and re-used across all objects; depth dilation for robustness to sparse point clouds; parallel per-object processing; adaptive surface sampling (optional) guided by camera viewpoint; relaxed FOV/ego-sector gating to reduce false negatives.

### Project Implementation
- Integration Method: visibility.dispatcher.compute_visibility delegates to the sphere method and returns per-camera visibility; fusion.py aggregates per-object camera scores into vis_avg/vis_max, visible views, and occlusion levels; integrated in robobus_vis/pipeline/run_batch.py with optional camera visualization.
- Performance Metrics: Instrumented logging for depth-map generation time per camera and total per-frame depth generation, plus overall per-object throughput via ThreadPoolExecutor; statistics include coverage ratio and variance across cameras (no fixed numeric targets yet).
- Custom Modifications: Removal of obsolete Z-buffer modules; safe-get for visibility.include_noFOV_as_zero; relaxed FOV and ego-sector configuration; diagnostics output directories for per-object/per-camera debugging.

## Project Operation
### Execution Methodology
- Runtime Environment: Python >= 3.10; OS: Windows and Ubuntu; dependencies listed in requirements.txt (no CUDA required).
- Startup Sequence:
  1) Prepare a RoboBUS-style clip directory with images, point cloud, calibration, and annotation JSON.
  2) Run batch pipeline to compute visibility and export augmented JSON: 
     python -m robobus_vis.pipeline.run_batch --clip_dir <path_to_clip> --config configs/default.yaml --viz_cam
  3) Optional: Inspect clip contents and calibration quickly: 
     python -m robobus_vis.pipeline.run_clip --clip_dir <path_to_clip> --config configs/default.yaml
- Workflow Diagram (textual): Load config -> load dataset + calibrations -> for each timestamp: build per-camera depth maps -> for each object: FOV gating -> sphere-projection visibility -> fuse + validate -> write augmented JSON -> optional image overlays + mosaics.

### Configuration System
- Key Configuration Files:
  - configs/default.yaml: Central configuration for visibility parameters, FOV/ego-sector gating, training filters, and camera mapping
  - requirements.txt: Python dependencies for deterministic environment setup
- Critical Parameters:
  - visibility.sphere_radius_m: Controls circular neighborhood radius in image space around projected surface samples; larger radius increases robustness to noise but can over-smooth occlusions
  - visibility.occlusion_fraction_thr: Threshold on fraction of samples deemed occluded to decide visibility per neighborhood, affecting per-camera visibility rate

## Project Architecture
### Hierarchical Structure
1. Presentation Layer: Visualization helpers and exporters for overlays and mosaics (robobus_vis/vis/draw2d.py, robobus_vis/vis/exporters.py, batch_visualization.py)
2. Business Logic: Visibility core (robobus_vis/visibility/sphere_projection.py, robobus_vis/visibility/dispatcher.py, robobus_vis/visibility/fusion.py, robobus_vis/visibility/fov_filter.py)
3. Data Access: Dataset/IO utilities for clips, calibration, and pose (robobus_vis/io/dataset_loader.py, robobus_vis/io/calib_parser.py, robobus_vis/io/pose_loader.py [optional])
4. Infrastructure: Geometry and camera models, depth-map projection, threading/logging, configs (robobus_vis/geometry/*, robobus_vis/calib/*, configs/default.yaml)
5. Visual verification pipeline: Batch pipeline and utilities for qualitative checks (robobus_vis/pipeline/run_batch.py, robobus_vis/pipeline/run_clip.py, test_visibility.py)

### Key Documentation
| Document | Description |
|----------|-------------|
| docs/sphere_projection_algorithm.md | Detailed description of Sphere Projection Visibility algorithm, parameters, and integration |
| README.md | High-level overview, environment requirements, and entry points |
| agent_work_log.md | Auditable change log and tool usage history across tasks |

## Development Roadmap
### Short-term Goals (Next 2 weeks)
- Validate T2 fix end-to-end and add regression test for missing config keys
- Solidify depth dilation and adaptive sampling defaults with ablations on small clips

<!-- ### Mid-term Objectives
- Expand diagnostic outputs (per-object/per-camera snapshots, histograms of depth residuals)
- Expose reliability/BEV weights for downstream training data selection

### Long-term Vision
- Integrate visibility-driven filtering and weights into BEV/Transformer training pipelines
- Extend to temporal consistency (clip-level smoothing) and optional motion compensation flow -->

## AI Interaction Protocol
This document enables AI Agents to:
1. Accurately comprehend project architecture
2. Perform code optimizations
3. Implement new features
4. Diagnose operational issues
5. Generate relevant documentation

Note: All placeholders have been replaced with project-specific information based on the current repository state.