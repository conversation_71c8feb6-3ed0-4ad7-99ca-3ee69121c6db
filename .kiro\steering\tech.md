# Technology Stack

## Core Technologies
- **Python**: >= 3.10 (primary language)
- **NumPy**: Numerical computations and array operations
- **OpenCV**: Computer vision and image processing (>=4.8)
- **Open3D**: 3D geometry processing and point cloud handling
- **Matplotlib**: Visualization and plotting
- **PyYAML**: Configuration file parsing
- **Numba**: JIT compilation for performance optimization
- **TQDM**: Progress bars for batch processing

## Additional Dependencies
- **SciPy**: Scientific computing utilities
- **PyQuaternion**: Quaternion operations for 3D transformations

## Build System
- **Package Management**: pip with requirements.txt
- **No Build Step Required**: Pure Python project
- **No CUDA Required**: CPU-only implementation using ThreadPoolExecutor

## Common Commands

### Environment Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Verify Python version
python --version  # Should be >= 3.10
```

### Running the Pipeline
```bash
# Batch processing with visualization
python -m robobus_vis.pipeline.run_batch --clip_dir <path_to_clip> --config configs/default.yaml --viz_cam

# Quick clip inspection
python -m robobus_vis.pipeline.run_clip --clip_dir <path_to_clip> --config configs/default.yaml

# Single frame testing
python test_single_frame.py

# Batch visualization (standard mode)
python batch_visualization.py --demo_data_dir visibility_demo_data/clip_dataset_1 --result_dir result_test/clip_dataset_1

# Batch visualization (hybrid mode - no FOV gating)
python batch_visualization.py --no_filter
```

### Testing
```bash
# Run visibility tests
python test_visibility.py

# Run FOV and visibility tests
python -m pytest tests/test_fov_and_visibility.py

# Test LiDAR BEV pipeline
python test_lidar_bev_pipeline.py
```

## Performance Considerations
- Multi-threaded CPU pipeline using ThreadPoolExecutor
- Per-frame, per-camera depth map caching for efficiency
- Parallel per-object processing
- Instrumented logging for performance monitoring
- No GPU dependencies - runs on Windows and Ubuntu