#!/usr/bin/env python3
"""
Test script for LiDAR BEV visualization pipeline integration
TRAE-MOD: [20250121-1430-test-pipeline] - Complete pipeline testing
"""

import os
import sys
import json
import numpy as np
import cv2
import logging
from pathlib import Path

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from visualization_occlusion_lidar_bev import LiDARBEVVisualizationProcessor
try:
    import open3d as o3d
except ImportError:
    print("Warning: Open3D not available. Point cloud tests will be skipped.")
    o3d = None

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_json_data():
    """Create sample JSON annotation data for testing"""
    sample_data = {
        "frame_id": "test_frame_001",
        "timestamp": 1642781234.567,
        "objects": [
            {
                "id": "obj_001",
                "class": "car",
                "position": {"x": 10.5, "y": 5.2, "z": 0.0},
                "dimensions": {"length": 4.5, "width": 2.0, "height": 1.8},
                "rotation": {"yaw": 0.3},
                "occlusion_level": 0
            },
            {
                "id": "obj_002", 
                "class": "pedestrian",
                "position": {"x": -15.0, "y": -8.5, "z": 0.0},
                "dimensions": {"length": 0.6, "width": 0.6, "height": 1.7},
                "rotation": {"yaw": 1.57},
                "occlusion_level": 1
            },
            {
                "id": "obj_003",
                "class": "truck", 
                "position": {"x": 25.0, "y": -12.0, "z": 0.0},
                "dimensions": {"length": 8.0, "width": 2.5, "height": 3.0},
                "rotation": {"yaw": -0.5},
                "occlusion_level": 2
            },
            {
                "id": "obj_004",
                "class": "bicycle",
                "position": {"x": -30.0, "y": 20.0, "z": 0.0},
                "dimensions": {"length": 1.8, "width": 0.6, "height": 1.2},
                "rotation": {"yaw": 2.1},
                "occlusion_level": 3
            }
        ]
    }
    return sample_data

def create_sample_point_cloud():
    """Create sample point cloud data for testing"""
    # Generate random points within the coordinate range
    np.random.seed(42)  # For reproducible results
    
    # Create points in different regions
    n_points = 5000
    
    # Road surface points
    road_x = np.random.uniform(-80, 80, n_points // 2)
    road_y = np.random.uniform(-50, 50, n_points // 2)
    road_z = np.random.uniform(-0.5, 0.5, n_points // 2)
    
    # Object points (higher z values)
    obj_x = np.random.uniform(-40, 40, n_points // 4)
    obj_y = np.random.uniform(-25, 25, n_points // 4)
    obj_z = np.random.uniform(0.5, 3.0, n_points // 4)
    
    # Background points
    bg_x = np.random.uniform(-80, 80, n_points // 4)
    bg_y = np.random.uniform(-50, 50, n_points // 4)
    bg_z = np.random.uniform(-2.0, 5.0, n_points // 4)
    
    # Combine all points
    points = np.column_stack([
        np.concatenate([road_x, obj_x, bg_x]),
        np.concatenate([road_y, obj_y, bg_y]),
        np.concatenate([road_z, obj_z, bg_z])
    ])
    
    return points

def test_coordinate_system():
    """Test coordinate system transformations"""
    logger.info("Testing coordinate system transformations...")
    
    processor = LiDARBEVVisualizationProcessor()
    
    # Test corner points
    test_points = np.array([
        [-80, -50],  # Bottom-left corner
        [80, -50],   # Bottom-right corner
        [-80, 50],   # Top-left corner
        [80, 50],    # Top-right corner
        [0, 0],      # Center
    ])
    
    logger.info(f"Image dimensions: {processor.img_width} x {processor.img_height}")
    logger.info(f"Pixels per meter: {processor.pixels_per_m}")
    
    for i, (x, y) in enumerate(test_points):
        pixel_x, pixel_y = processor._world_to_pixel(x, y)
        logger.info(f"Point {i+1}: World({x:6.1f}, {y:6.1f}) -> Pixel({pixel_x:6.1f}, {pixel_y:6.1f})")
    
    return True

def test_visualization_pipeline():
    """Test complete visualization pipeline"""
    logger.info("Testing complete visualization pipeline...")
    
    try:
        # Initialize processor
        processor = LiDARBEVVisualizationProcessor()
        
        # Create sample data files
        json_data = create_sample_json_data()
        point_cloud = create_sample_point_cloud()
        
        # Save sample data to temporary files
        temp_json_path = "temp_test_data.json"
        temp_pcd_path = "temp_test_data.pcd"
        
        # Save JSON data
        with open(temp_json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        logger.info(f"Created temporary JSON file: {temp_json_path}")
        
        # Save point cloud data using Open3D
        try:
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(point_cloud)
            o3d.io.write_point_cloud(temp_pcd_path, pcd)
            logger.info(f"Created temporary PCD file: {temp_pcd_path}")
        except Exception as e:
            logger.warning(f"Failed to create PCD file: {e}. Testing without point cloud.")
            temp_pcd_path = None
        
        # Test single frame processing
        logger.info("Processing single frame with sample data...")
        output_path = "test_lidar_bev_output.png"
        result_image = processor.process_single_frame(
            json_path=temp_json_path,
            pcd_path=temp_pcd_path,
            output_path=output_path
        )
        
        # Clean up temporary files
        try:
            if os.path.exists(temp_json_path):
                os.remove(temp_json_path)
            if temp_pcd_path and os.path.exists(temp_pcd_path):
                os.remove(temp_pcd_path)
        except Exception as e:
            logger.warning(f"Failed to clean up temporary files: {e}")
        
        if result_image is not None:
            logger.info(f"Test output saved to: {output_path}")
            
            # Verify image properties
            height, width = result_image.shape[:2]
            logger.info(f"Output image size: {width} x {height}")
            
            # Check if image has content (not all black)
            non_zero_pixels = np.count_nonzero(result_image)
            total_pixels = height * width * (3 if len(result_image.shape) == 3 else 1)
            content_ratio = non_zero_pixels / total_pixels
            logger.info(f"Content ratio: {content_ratio:.3f} ({non_zero_pixels}/{total_pixels} non-zero pixels)")
            
            return True, output_path
        else:
            logger.error("Failed to generate visualization")
            return False, None
            
    except Exception as e:
        logger.error(f"Pipeline test failed: {str(e)}")
        return False, None

def test_error_handling():
    """Test error handling capabilities"""
    logger.info("Testing error handling...")
    
    processor = LiDARBEVVisualizationProcessor()
    
    # Test with non-existent file
    try:
        result1 = processor.process_single_frame(json_path="non_existent_file.json", pcd_path=None)
        logger.info(f"Non-existent file test: {'PASS' if result1 is None else 'FAIL'}")
    except Exception as e:
        logger.info(f"Non-existent file test: PASS (handled exception: {type(e).__name__})")
    
    # Test with empty JSON file
    try:
        empty_json_path = "temp_empty.json"
        with open(empty_json_path, 'w') as f:
            json.dump({"objects": []}, f)
        
        result2 = processor.process_single_frame(json_path=empty_json_path, pcd_path=None)
        logger.info(f"Empty JSON test: {'PASS' if result2 is not None else 'FAIL'}")
        
        # Clean up
        if os.path.exists(empty_json_path):
            os.remove(empty_json_path)
    except Exception as e:
        logger.info(f"Empty JSON test: PASS (handled exception: {type(e).__name__})")
    
    # Test with malformed JSON file
    try:
        malformed_json_path = "temp_malformed.json"
        with open(malformed_json_path, 'w') as f:
            f.write('{"invalid_key": "invalid_value"}')
        
        result3 = processor.process_single_frame(json_path=malformed_json_path, pcd_path=None)
        logger.info(f"Malformed JSON test: {'PASS' if result3 is not None else 'FAIL'}")
        
        # Clean up
        if os.path.exists(malformed_json_path):
            os.remove(malformed_json_path)
    except Exception as e:
        logger.info(f"Malformed JSON test: PASS (handled exception: {type(e).__name__})")
    
    return True

def main():
    """Main test function"""
    logger.info("Starting LiDAR BEV visualization pipeline tests...")
    
    # Test 1: Coordinate system
    try:
        coord_test = test_coordinate_system()
        logger.info(f"Coordinate system test: {'PASS' if coord_test else 'FAIL'}")
    except Exception as e:
        logger.error(f"Coordinate system test failed: {str(e)}")
        coord_test = False
    
    # Test 2: Complete pipeline
    try:
        pipeline_test, output_file = test_visualization_pipeline()
        logger.info(f"Pipeline test: {'PASS' if pipeline_test else 'FAIL'}")
        if output_file:
            logger.info(f"Test visualization saved: {output_file}")
    except Exception as e:
        logger.error(f"Pipeline test failed: {str(e)}")
        pipeline_test = False
    
    # Test 3: Error handling
    try:
        error_test = test_error_handling()
        logger.info(f"Error handling test: {'PASS' if error_test else 'FAIL'}")
    except Exception as e:
        logger.error(f"Error handling test failed: {str(e)}")
        error_test = False
    
    # Summary
    total_tests = 3
    passed_tests = sum([coord_test, pipeline_test, error_test])
    
    logger.info(f"\n=== TEST SUMMARY ===")
    logger.info(f"Total tests: {total_tests}")
    logger.info(f"Passed: {passed_tests}")
    logger.info(f"Failed: {total_tests - passed_tests}")
    logger.info(f"Success rate: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        logger.info("All tests PASSED! Pipeline is ready for production.")
        return 0
    else:
        logger.warning("Some tests FAILED. Please review the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)