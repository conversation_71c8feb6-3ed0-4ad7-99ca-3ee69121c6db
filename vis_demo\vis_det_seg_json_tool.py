import os
import numpy as np
import open3d as o3d
import open3d.visualization.gui as gui
import yaml
import json
from collections import Counter

# Define color dictionary for major categories
COLOR_DICT = {
    "ignore": [0, 0, 0],        # black
    "person": [255, 255, 0],    # yellow
    "bike": [204, 153, 255],    # purple
    "rider": [255, 127, 0],     # orange
    "car": [0, 255, 255],       # light blue
    "truck": [0, 0, 255],       # deep blue
    "bus": [153, 0, 153],       # deep purple
    "curb": [205, 155, 29],     # goldenrod
    "building": [105, 105, 105], # deep gray
    "vegetation": [0, 205, 0],   # green
    "fence": [139, 0, 0],       # dark red
    "obstacle": [255, 105, 180], # light pink
    "noise": [131, 111, 255],   # purple blue
    "ground": [0, 0, 0]         # black
}

def load_yaml_config(yaml_path):
    """Load category mapping from YAML file."""
    try:
        with open(yaml_path, 'r') as file:
            yaml_data = yaml.safe_load(file)
            label_map_inv = {int(k): v for k, v in yaml_data.get('label_map_inv', {}).items()}
            learning_map = {int(k): v for k, v in yaml_data.get('learning_map', {}).items()}
        return learning_map, label_map_inv
    except Exception as e:
        print(f"Error loading YAML file: {e}")
        return None, None

def load_pcd_and_json_data(pcd_path, json_path, learning_map, label_map_inv):
    """Load point cloud data and detection results."""
    # 手动解析PCD文件
    try:
        points = []
        labels = []
        header_ended = False
        with open(pcd_path, 'r') as f:
            for line in f:
                # 跳过文件头
                if line.startswith("DATA"):
                    header_ended = True
                    continue
                if not header_ended:
                    continue

                # 解析数据行（ASCII格式）
                parts = line.strip().split()
                if len(parts) < 5:
                    continue

                # 提取坐标和标签（最后一个是标签字段）
                x, y, z = map(float, parts[0:3])
                label_str = parts[-1].zfill(4)  # 保证4位字符串

                # 处理标签：去掉前导0并转换为整数
                if label_str.startswith('0') and len(label_str) == 4:
                    label = int(label_str[1:])  # 去掉第一个0
                else:
                    label = int(label_str)

                points.append([x, y, z])
                labels.append(label)

        # 转换为numpy数组
        points = np.array(points, dtype=np.float32)
        labels = np.array(labels, dtype=int)
        mapped_labels = np.vectorize(lambda x: learning_map.get(x, 0))(labels)

    except Exception as e:
        print(f"Error loading PCD file: {e}")
        return None

    # Load detection results
    try:
        with open(json_path, 'r') as f:
            detection_data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return None

    # Parse detection results
    bboxes = []
    bboxes_label = []
    category_names = []

    for obj in detection_data.get('result', {}).get('data', []):
        center = obj.get('3Dcenter', {})
        size = obj.get('3Dsize', {})
        if center and size:
            x, y, z = center.get('x', 0), center.get('y', 0), center.get('z', 0)
            width = size.get('width', 0)
            length = size.get('length', 0)
            height = size.get('height', 0)
            rz = size.get('rz', 0)

            # Get original label and map it through learning_map and label_map_inv
            original_label = int(obj.get('label', 0))
            major_category = learning_map.get(original_label, 0)
            category_name = label_map_inv.get(major_category, "ignore")

            bboxes.append([x, y, z, length, width, height, rz])
            bboxes_label.append(major_category)
            category_names.append(category_name)

    return {
        'points': points,
        'bboxes': np.array(bboxes),
        'bboxes_label': bboxes_label,
        'category_names': category_names,
        'mapped_labels': mapped_labels
    }

def translate_boxes_to_open3d_instance(boxes_3d):
    """Convert bounding box parameters to Open3D line set."""
    center = boxes_3d[0:3]
    lwh = boxes_3d[3:6]
    axis_angles = np.array([0, 0, boxes_3d[6] + 1e-10])
    rot = o3d.geometry.get_rotation_matrix_from_axis_angle(axis_angles)
    box3d = o3d.geometry.OrientedBoundingBox(center, rot, lwh)

    line_set = o3d.geometry.LineSet.create_from_oriented_bounding_box(box3d)
    lines = np.asarray(line_set.lines)
    lines = np.concatenate([lines, np.array([[1, 4], [7, 6]])], axis=0)
    line_set.lines = o3d.utility.Vector2iVector(lines)

    return line_set, box3d

def points_in_box(points, box):
    """Check which points are inside a rotated 3D bounding box."""
    box_points = np.asarray(points)
    center = box.center
    R = box.R
    extent = box.extent

    # Transform points to local box coordinates
    points_local = (R.T @ (box_points - center).T).T

    # Check if points are within box bounds
    mask = np.all(np.abs(points_local) <= extent/2, axis=1)
    return mask

def visualize_detections(points, pcd_labels, bboxes, category_names, label_map_inv):
    """Visualize point cloud with segmentation colors and detection boxes."""
    # Create point cloud object
    pcd_o3d = o3d.geometry.PointCloud()
    pcd_o3d.points = o3d.utility.Vector3dVector(points)

    # 修改颜色生成逻辑：将类别编码转换为名称再取颜色
    pcd_colors = np.array([
        COLOR_DICT.get(label_map_inv.get(label, "ignore"), [0, 0, 0]) 
        for label in pcd_labels
    ]) / 255.0

    # Color points inside detection boxes
    for bbox, category in zip(bboxes, category_names):
        center = bbox[:3]
        lwh = bbox[3:6]
        rotation = o3d.geometry.get_rotation_matrix_from_axis_angle([0, 0, bbox[6] + 1e-10])
        box3d = o3d.geometry.OrientedBoundingBox(center, rotation, lwh)

        mask = points_in_box(points, box3d)
        box_color = np.array(COLOR_DICT.get(category, [128, 128, 128])) / 255.0
        pcd_colors[mask] = box_color

    # Assign colors to point cloud
    pcd_o3d.colors = o3d.utility.Vector3dVector(pcd_colors)

    # Initialize visualizer
    vis = o3d.visualization.O3DVisualizer("Point Cloud Visualization", 1024, 768)
    vis.add_geometry("PointCloud", pcd_o3d)

    # Add bounding boxes
    for i, (bbox, category) in enumerate(zip(bboxes, category_names)):
        line_set, box3d = translate_boxes_to_open3d_instance(bbox)
        line_color = np.array(COLOR_DICT.get(category, [255, 255, 255])) / 255.0
        line_colors = np.tile(line_color, (len(line_set.lines), 1))
        line_set.colors = o3d.utility.Vector3dVector(line_colors)
        vis.add_geometry(f"box_{i}", line_set)
        vis.add_3d_label(box3d.get_box_points()[5], f"{category}_{i}")

    vis.reset_camera_to_default()
    vis.show_settings = True
    return vis

def analyze_detections(category_names):
    """Analyze and print detection statistics."""
    det_counter = Counter(category_names)
    print("\nObject Detection Statistics:")
    for category, count in det_counter.items():
        print(f"Category {category}: {count} boxes")

def main():
    # Initialize Open3D application
    app = gui.Application.instance
    app.initialize()

    # Set paths
    dataset_dir = r"D:\Data\0311\20241227160618-1735286778641254808-full-JLHYD01777D_6"
    pcd_dir = os.path.join(dataset_dir, 'result_pcd')
    json_dir = os.path.join(dataset_dir, 'result_json')
    yaml_path = "falcon.yaml"

    # Load category mapping
    learning_map, label_map_inv = load_yaml_config(yaml_path)
    if learning_map is None or label_map_inv is None:
        print("Failed to load category mapping")
        return

    # Process each PCD file
    for pcd_file in sorted(os.listdir(pcd_dir)):
        if not pcd_file.endswith('.pcd'):
            continue

        pcd_path = os.path.join(pcd_dir, pcd_file)
        json_path = os.path.join(json_dir, pcd_file.replace('.pcd', '.json'))

        print(f"\nProcessing: {pcd_file}")

        # Load data
        data = load_pcd_and_json_data(pcd_path, json_path, learning_map, label_map_inv)
        if data is None:
            continue

        # Analyze detections
        analyze_detections(data['category_names'])

        # Visualize
        vis = visualize_detections(
            data['points'],
            data['mapped_labels'],
            data['bboxes'],
            data['category_names'],
            label_map_inv
        )

        # Display and wait for window close
        app.add_window(vis)
        app.run()

if __name__ == "__main__":
    main()