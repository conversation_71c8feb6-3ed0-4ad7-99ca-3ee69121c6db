from typing import Any, Di<PERSON>, <PERSON><PERSON>, Optional
import numpy as np
import logging

from .sphere_projection import visibility_via_sphere_projection
from .conservative_validation import (
    is_in_camera_fov_loose, 
    conservative_depth_validation,
    get_conservative_config_safe,
    validate_conservative_config
)
from ..config.safe_access import (
    get_safe_config_value,
    get_conservative_validation_config,
    get_visibility_config,
    get_camera_config
)

logger = logging.getLogger(__name__)


def compute_visibility(
    box3d: Dict,
    cam,
    depth_img: np.ndarray,
    cfg: Dict,
    method_arg: str,
    samples: int,
    enable_diagnostics: bool,
    debug_dir: Optional[str],
    obj_id: Any,
    cam_name: str,
) -> Tuple[float, Dict]:
    """
    Enhanced visibility computation with optional conservative validation.

    Combines sphere projection (baseline) with conservative validation when enabled.
    Conservative validation provides additional robustness against calibration errors
    and sparse depth data by using generous tolerances and fallback strategies.

    Notes:
    - The dispatcher maintains backward compatibility with existing callers
    - Conservative validation is optional and controlled via configuration
    - When enabled, takes maximum of sphere projection and conservative validation scores
    - Configuration keys read from cfg['visibility'] and cfg['conservative_validation']
    """
    # Use safe configuration access patterns
    try:
        # Get camera-specific configuration with safe defaults and overrides
        eff_cfg = get_camera_config(cfg, cam_name)
        
        # Get conservative validation configuration safely
        conservative_cfg = get_conservative_validation_config(cfg)
        conservative_enabled = conservative_cfg['enabled']
        
    except Exception as e:
        logger.error(f"Failed to load configuration safely: {e}")
        # Fallback to basic configuration
        eff_cfg = get_visibility_config(cfg)
        conservative_cfg = {'enabled': False, 'azimuth_tolerance_deg': 10.0, 'min_depth_points': 5, 'tau_multiplier': 1.5}
        conservative_enabled = False

    # Validate conservative configuration if enabled
    if conservative_enabled:
        try:
            is_valid, error_msg = validate_conservative_config(cfg)
            if not is_valid:
                logger.warning(f"Conservative validation config invalid: {error_msg}. Disabling conservative validation.")
                conservative_enabled = False
                conservative_cfg['enabled'] = False
        except Exception as e:
            logger.warning(f"Conservative validation config validation failed: {e}. Disabling conservative validation.")
            conservative_enabled = False
            conservative_cfg['enabled'] = False

    # Optional parameter validation warning using safe access
    try:
        tau_base = get_safe_config_value(eff_cfg, 'tau_base_m', 2.0)
        sphere_radius = get_safe_config_value(eff_cfg, 'sphere_radius_m', 0.15)
        
        if tau_base > 0.6:
            logger.warning(f"tau_base_m={tau_base} exceeds recommended maximum (0.6) for camera {cam_name}")
        if sphere_radius > 0.25:
            logger.warning(f"sphere_radius_m={sphere_radius} exceeds recommended maximum (0.25) for camera {cam_name}")
    except Exception as e:
        logger.debug(f"Parameter validation warning failed: {e}")
        pass

    # Baseline sphere projection visibility using safe configuration access
    sphere_vis, sphere_stats = visibility_via_sphere_projection(
        box3d,
        cam,
        depth_img,
        sphere_radius_m=get_safe_config_value(eff_cfg, 'sphere_radius_m', 0.15),
        tau_base_m=get_safe_config_value(eff_cfg, 'tau_base_m', 2.0),
        tau_scale_per_m=get_safe_config_value(eff_cfg, 'tau_scale_per_m', 0.1),
        occlusion_fraction_thr=get_safe_config_value(eff_cfg, 'occlusion_fraction_thr', 0.2),
        min_neighbors=get_safe_config_value(eff_cfg, 'min_neighbors', 3),
        treat_no_depth_as_visible=get_safe_config_value(eff_cfg, 'treat_no_depth_as_visible', True),
        max_window_px=get_safe_config_value(eff_cfg, 'max_window_px', 15),
    )

    # Initialize final visibility and statistics
    final_vis = sphere_vis
    final_stats = sphere_stats.copy()
    
    # Add sphere visibility to diagnostics
    final_stats['sphere_vis'] = sphere_vis
    final_stats['conservative_enabled'] = conservative_enabled

    # Optional conservative validation
    if conservative_enabled:
        try:
            # Extract box3d parameters
            center = np.array([box3d['x'], box3d['y'], box3d['z']], dtype=float)
            dims = np.array([box3d['l'], box3d['w'], box3d['h']], dtype=float)
            yaw = float(box3d.get('yaw', 0.0))

            # Perform loose FOV check first using safe configuration access
            in_loose_fov = is_in_camera_fov_loose(
                center, 
                cam, 
                tolerance_deg=get_safe_config_value(conservative_cfg, 'azimuth_tolerance_deg', 10.0)
            )
            
            final_stats['in_loose_fov'] = in_loose_fov

            if in_loose_fov:
                # Perform conservative depth validation
                conservative_vis = conservative_depth_validation(
                    center, dims, yaw, cam, depth_img, cfg
                )
                
                # Take maximum to reduce false negatives
                final_vis = max(sphere_vis, conservative_vis)
                
                # Add conservative validation statistics
                final_stats['conservative_vis'] = conservative_vis
                final_stats['fusion_method'] = 'max'
                final_stats['vis_improvement'] = final_vis - sphere_vis
                
                logger.debug(f"Conservative validation for {cam_name}: "
                           f"sphere={sphere_vis:.3f}, conservative={conservative_vis:.3f}, "
                           f"final={final_vis:.3f}")
            else:
                # Object not in loose FOV, use sphere projection only
                final_stats['conservative_vis'] = None
                final_stats['fusion_method'] = 'sphere_only'
                final_stats['vis_improvement'] = 0.0
                
                logger.debug(f"Object not in loose FOV for {cam_name}, using sphere projection only")
                
        except Exception as e:
            logger.warning(f"Conservative validation failed for {cam_name}: {e}. Using sphere projection only.")
            final_stats['conservative_vis'] = None
            final_stats['fusion_method'] = 'sphere_fallback'
            final_stats['vis_improvement'] = 0.0
            final_stats['conservative_error'] = str(e)
    else:
        # Conservative validation disabled
        final_stats['conservative_vis'] = None
        final_stats['fusion_method'] = 'sphere_only'
        final_stats['vis_improvement'] = 0.0

    # Add diagnostic information if enabled
    if enable_diagnostics:
        final_stats['cam_name'] = cam_name
        final_stats['obj_id'] = obj_id
        final_stats['method_used'] = 'sphere_projection_with_conservative' if conservative_enabled else 'sphere_projection'

    return final_vis, final_stats

