#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D Occlusion Visualization Tool with Enhanced Exit Mechanisms

This script visualizes 3D detection results with occlusion level coloring.
Enhanced with multiple exit mechanisms for better user control.

Author: <PERSON>ro AI Assistant
Date: 2025-08-27
"""

import os
import sys
import signal
import threading
import time
import numpy as np
import open3d as o3d
import open3d.visualization.gui as gui
import json
from collections import Counter

# Global control variables
class VisualizationController:
    def __init__(self):
        self.should_exit = False
        self.current_window = None
        self.app = None
        self.user_choice = None
        self.choice_event = threading.Event()
    
    def request_exit(self):
        """Request program exit"""
        self.should_exit = True
        if self.current_window:
            try:
                self.current_window.close()
            except:
                pass
        if self.app:
            try:
                self.app.quit()
            except:
                pass

# Global controller instance
controller = VisualizationController()

def signal_handler(signum, frame):
    """Handle Ctrl+C signal gracefully"""
    print("\n\n🛑 Interrupt signal received (Ctrl+C)")
    print("Gracefully shutting down visualization...")
    controller.request_exit()
    sys.exit(0)

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)

# Define occlusion level color mapping
OCCLUSION_COLORS = {
    1: [0, 255, 0],      # Green for low occlusion (visible)
    2: [0, 255, 0],      # Green for low occlusion (visible)
    3: [255, 255, 0],    # Yellow for medium occlusion (severely occluded)
    4: [255, 0, 0],      # Red for high occlusion (invisible/completely occluded)
    0: [128, 128, 128]   # Gray for unknown/default
}

def load_pcd_and_json_data(pcd_path, json_path):
    """
    Load point cloud data and detection results with occlusion levels.
    Compatible with multiple JSON structures and robust error handling.
    """
    # Check if exit was requested
    if controller.should_exit:
        return None
        
    # Load point cloud using Open3D native functions
    try:
        pcd = o3d.io.read_point_cloud(pcd_path)
        points = np.asarray(pcd.points)
        
        if len(points) == 0:
            print(f"Warning: Empty point cloud in {pcd_path}")
            return None
            
        print(f"  Loaded point cloud: {len(points)} points")
            
    except Exception as e:
        print(f"Error loading PCD file {pcd_path}: {e}")
        return None

    # Load detection results from JSON
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            detection_data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON file {json_path}: {e}")
        return None

    # Parse detection results and extract occlusion levels
    bboxes = []
    occlusion_levels = []
    object_ids = []
    labels = []

    # Handle different JSON structures for maximum compatibility
    objects_data = None
    if 'result' in detection_data and 'data' in detection_data['result']:
        objects_data = detection_data['result']['data']
        print(f"  Using JSON structure: result.data")
    elif 'objects' in detection_data:
        objects_data = detection_data['objects']
        print(f"  Using JSON structure: objects")
    elif 'data' in detection_data:
        objects_data = detection_data['data']
        print(f"  Using JSON structure: data")
    else:
        print(f"Warning: Unknown JSON structure in {json_path}")
        print(f"Available keys: {list(detection_data.keys())}")
        return None

    if not objects_data:
        print(f"Warning: No objects found in {json_path}")
        return None

    valid_objects = 0
    for obj_idx, obj in enumerate(objects_data):
        # Check if exit was requested during processing
        if controller.should_exit:
            return None
            
        try:
            # Extract 3D center and size with error checking
            center = obj.get('3Dcenter', {})
            size = obj.get('3Dsize', {})
            
            if not center or not size:
                print(f"  Warning: Object {obj_idx} missing 3D center or size data")
                continue
                
            # Extract coordinates with defaults
            x = center.get('x', 0)
            y = center.get('y', 0) 
            z = center.get('z', 0)
            width = size.get('width', 0)
            length = size.get('length', 0)
            height = size.get('height', 0)
            
            # Handle rotation - try multiple field names for compatibility
            rz = size.get('rz', size.get('alpha', size.get('rotation_z', 0)))

            # Extract occlusion level from visibility data
            visibility = obj.get('visibility', {})
            occlusion_level = visibility.get('occlusion_level', 0)
            
            # Validate occlusion level
            if occlusion_level not in [0, 1, 2, 3, 4]:
                print(f"  Warning: Invalid occlusion level {occlusion_level} for object {obj_idx}, using 0")
                occlusion_level = 0
            
            # Extract other metadata with defaults
            object_id = obj.get('ObjectID', obj.get('object_id', f'obj_{obj_idx}'))
            label = obj.get('label', obj.get('class', 'unknown'))

            # Validate bounding box dimensions
            if width <= 0 or length <= 0 or height <= 0:
                print(f"  Warning: Invalid dimensions for object {obj_idx}: {width}x{length}x{height}")
                continue

            bboxes.append([x, y, z, length, width, height, rz])
            occlusion_levels.append(occlusion_level)
            object_ids.append(object_id)
            labels.append(label)
            valid_objects += 1

        except Exception as e:
            print(f"  Warning: Error processing object {obj_idx}: {e}")
            continue

    print(f"  Parsed {valid_objects} valid objects from {len(objects_data)} total objects")

    if valid_objects == 0:
        print(f"Warning: No valid objects found in {json_path}")
        return None

    return {
        'points': points,
        'bboxes': np.array(bboxes),
        'occlusion_levels': occlusion_levels,
        'object_ids': object_ids,
        'labels': labels
    }

def translate_boxes_to_open3d_instance(boxes_3d):
    """Convert bounding box parameters to Open3D line set."""
    center = boxes_3d[0:3]
    lwh = boxes_3d[3:6]
    axis_angles = np.array([0, 0, boxes_3d[6] + 1e-10])
    rot = o3d.geometry.get_rotation_matrix_from_axis_angle(axis_angles)
    box3d = o3d.geometry.OrientedBoundingBox(center, rot, lwh)

    line_set = o3d.geometry.LineSet.create_from_oriented_bounding_box(box3d)
    lines = np.asarray(line_set.lines)
    # Add additional lines for better box visualization
    lines = np.concatenate([lines, np.array([[1, 4], [7, 6]])], axis=0)
    line_set.lines = o3d.utility.Vector2iVector(lines)

    return line_set, box3d

def points_in_box(points, box):
    """Check which points are inside a rotated 3D bounding box."""
    box_points = np.asarray(points)
    center = box.center
    R = box.R
    extent = box.extent

    # Transform points to local box coordinates
    points_local = (R.T @ (box_points - center).T).T

    # Check if points are within box bounds
    mask = np.all(np.abs(points_local) <= extent/2, axis=1)
    return mask

def get_user_choice():
    """Get user choice for next action"""
    print("\n" + "="*50)
    print("VISUALIZATION CONTROLS")
    print("="*50)
    print("Choose your next action:")
    print("  [N] Next frame")
    print("  [Q] Quit program")
    print("  [S] Skip to specific frame")
    print("  [A] Auto-play remaining frames")
    print("="*50)
    
    while True:
        try:
            choice = input("Enter your choice (N/Q/S/A): ").strip().upper()
            if choice in ['N', 'Q', 'S', 'A']:
                return choice
            else:
                print("Invalid choice. Please enter N, Q, S, or A.")
        except (EOFError, KeyboardInterrupt):
            return 'Q'

def create_enhanced_visualizer(points, bboxes, occlusion_levels, object_ids, labels, frame_info):
    """Create enhanced visualizer with exit controls"""
    
    # Create point cloud object
    pcd_o3d = o3d.geometry.PointCloud()
    pcd_o3d.points = o3d.utility.Vector3dVector(points)

    # Initialize point colors to light gray
    pcd_colors = np.full((len(points), 3), [0.7, 0.7, 0.7])

    # Color points inside detection boxes based on occlusion level
    for bbox, occlusion_level in zip(bboxes, occlusion_levels):
        if controller.should_exit:
            return None
            
        center = bbox[:3]
        lwh = bbox[3:6]
        rotation = o3d.geometry.get_rotation_matrix_from_axis_angle([0, 0, bbox[6] + 1e-10])
        box3d = o3d.geometry.OrientedBoundingBox(center, rotation, lwh)

        # Find points inside this box
        mask = points_in_box(points, box3d)
        
        # Get color based on occlusion level
        box_color = np.array(OCCLUSION_COLORS.get(occlusion_level, OCCLUSION_COLORS[0])) / 255.0
        pcd_colors[mask] = box_color

    # Assign colors to point cloud
    pcd_o3d.colors = o3d.utility.Vector3dVector(pcd_colors)

    # Create visualizer with enhanced title
    title = f"3D Occlusion Visualization - {frame_info} (Press ESC or close window to continue)"
    vis = o3d.visualization.O3DVisualizer(title, 1024, 768)
    
    # Store reference for controller
    controller.current_window = vis
    
    vis.add_geometry("PointCloud", pcd_o3d)

    # Add bounding boxes with occlusion-level coloring
    for i, (bbox, occlusion_level, object_id, label) in enumerate(zip(bboxes, occlusion_levels, object_ids, labels)):
        if controller.should_exit:
            return None
            
        line_set, box3d = translate_boxes_to_open3d_instance(bbox)
        
        # Color box lines based on occlusion level
        line_color = np.array(OCCLUSION_COLORS.get(occlusion_level, OCCLUSION_COLORS[0])) / 255.0
        line_colors = np.tile(line_color, (len(line_set.lines), 1))
        line_set.colors = o3d.utility.Vector3dVector(line_colors)
        
        vis.add_geometry(f"box_{i}", line_set)
        
        # Add 3D label with occlusion level information
        try:
            label_text = f"ID:{object_id} Occ:{occlusion_level}"
            label_position = box3d.get_box_points()[5]  # Top corner
            vis.add_3d_label(label_position, label_text)
        except Exception as e:
            print(f"Warning: Could not add 3D label for object {object_id}: {e}")

    # Set up camera view
    vis.reset_camera_to_default()
    vis.show_settings = True
    
    return vis

def analyze_occlusion_levels(occlusion_levels):
    """Analyze and print occlusion level statistics."""
    occlusion_counter = Counter(occlusion_levels)
    print("\nOcclusion Level Statistics:")
    for level in sorted(occlusion_counter.keys()):
        count = occlusion_counter[level]
        if level == 1 or level == 2:
            status = "Visible"
        elif level == 3:
            status = "Severely Occluded"
        elif level == 4:
            status = "Invisible/Completely Occluded"
        else:
            status = "Unknown"
        print(f"Level {level} ({status}): {count} objects")

def print_color_legend():
    """Print color legend for occlusion levels."""
    print("\n" + "="*50)
    print("OCCLUSION LEVEL COLOR LEGEND")
    print("="*50)
    print("Level 1-2 (Visible):              GREEN")
    print("Level 3 (Severely Occluded):      YELLOW") 
    print("Level 4 (Invisible/Occluded):     RED")
    print("Level 0 (Unknown):                GRAY")
    print("="*50)

def print_exit_instructions():
    """Print exit instructions"""
    print("\n" + "="*60)
    print("EXIT INSTRUCTIONS")
    print("="*60)
    print("🔹 Close visualization window: Continue to next frame")
    print("🔹 Press Ctrl+C in console: Exit entire program")
    print("🔹 Choose 'Q' in menu: Quit program")
    print("🔹 Choose 'A' in menu: Auto-play all remaining frames")
    print("="*60)

def validate_paths(pcd_dir, json_dir):
    """Validate that the specified directories exist and contain files."""
    if not os.path.exists(pcd_dir):
        return False, f"PCD directory not found: {pcd_dir}"
    
    if not os.path.exists(json_dir):
        return False, f"JSON directory not found: {json_dir}"
    
    pcd_files = [f for f in os.listdir(pcd_dir) if f.endswith('.pcd')]
    if not pcd_files:
        return False, f"No PCD files found in {pcd_dir}"
    
    json_files = [f for f in os.listdir(json_dir) if f.endswith('.json')]
    if not json_files:
        return False, f"No JSON files found in {json_dir}"
    
    return True, f"Found {len(pcd_files)} PCD files and {len(json_files)} JSON files"

def main():
    """
    Enhanced main function with multiple exit mechanisms.
    """
    # Setup signal handlers for graceful shutdown
    setup_signal_handlers()
    
    # Initialize Open3D application
    app = gui.Application.instance
    app.initialize()
    controller.app = app

    # Set paths
    base_dir = r"D:\Data\B2-2024\clip_dataset_3"
    possible_pcd_dirs = [
        r"D:\Data\B2-2024\clip_dataset_3\3d_url",
        os.path.join(base_dir, "visibility_demo_data", "clip_dataset_2", "result_pcd"),
        os.path.join(base_dir, "visibility_demo_data", "clip_dataset_2", "3d_url"),
    ]
    
    json_dir = r"D:\Data\B2-2024\clip_dataset_3\occlusion_result\clip_dataset_3"
    
    # Find the first existing PCD directory
    pcd_dir = None
    for possible_dir in possible_pcd_dirs:
        if os.path.exists(possible_dir):
            pcd_files_test = [f for f in os.listdir(possible_dir) if f.endswith('.pcd')]
            if pcd_files_test:
                pcd_dir = possible_dir
                print(f"Found PCD directory: {pcd_dir}")
                break
    
    if pcd_dir is None:
        print("Error: Could not find PCD directory in any of the expected locations:")
        for possible_dir in possible_pcd_dirs:
            print(f"  - {possible_dir}")
        return

    print("🎯 3D Occlusion Visualization Tool - Enhanced Version")
    print("Based on vis_det_seg_json_tool.py with exit mechanisms")
    print_color_legend()
    print_exit_instructions()

    # Validate paths
    is_valid, message = validate_paths(pcd_dir, json_dir)
    if not is_valid:
        print(f"\nError: {message}")
        print("\nPlease check the following paths:")
        print(f"PCD directory: {pcd_dir}")
        print(f"JSON directory: {json_dir}")
        return
    
    print(f"\nValidation successful: {message}")

    # Process each PCD file with enhanced control
    pcd_files = [f for f in os.listdir(pcd_dir) if f.endswith('.pcd')]
    processed_count = 0
    error_count = 0
    auto_play = False

    try:
        for i, pcd_file in enumerate(sorted(pcd_files)):
            # Check for exit request
            if controller.should_exit:
                print("\n🛑 Exit requested. Stopping visualization...")
                break
                
            pcd_path = os.path.join(pcd_dir, pcd_file)
            json_path = os.path.join(json_dir, pcd_file.replace('.pcd', '.json'))

            print(f"\n{'='*60}")
            print(f"Processing: {pcd_file} ({i+1}/{len(pcd_files)})")
            print(f"{'='*60}")

            # Check if corresponding JSON file exists
            if not os.path.exists(json_path):
                print(f"Warning: JSON file not found: {json_path}")
                error_count += 1
                continue

            try:
                # Load data
                data = load_pcd_and_json_data(pcd_path, json_path)
                if data is None or controller.should_exit:
                    if controller.should_exit:
                        break
                    print(f"Failed to load data for {pcd_file}")
                    error_count += 1
                    continue

                print(f"Successfully loaded:")
                print(f"  - Point cloud: {len(data['points'])} points")
                print(f"  - Detection boxes: {len(data['bboxes'])} objects")

                # Analyze occlusion levels
                analyze_occlusion_levels(data['occlusion_levels'])

                # Create enhanced visualizer
                frame_info = f"Frame {i+1}/{len(pcd_files)}: {pcd_file}"
                vis = create_enhanced_visualizer(
                    data['points'],
                    data['bboxes'],
                    data['occlusion_levels'],
                    data['object_ids'],
                    data['labels'],
                    frame_info
                )

                if vis is None or controller.should_exit:
                    break

                print(f"\n🎬 Displaying visualization for {pcd_file}")
                
                if not auto_play:
                    print("Close the visualization window to continue...")

                # Display visualization
                app.add_window(vis)
                app.run()
                
                processed_count += 1
                controller.current_window = None

                # Check for exit request after window closes
                if controller.should_exit:
                    break

                # Get user choice (unless in auto-play mode)
                if not auto_play and i < len(pcd_files) - 1:  # Don't ask after last frame
                    choice = get_user_choice()
                    
                    if choice == 'Q':
                        print("🛑 User requested quit. Exiting...")
                        break
                    elif choice == 'S':
                        try:
                            skip_to = int(input(f"Enter frame number to skip to (1-{len(pcd_files)}): "))
                            if 1 <= skip_to <= len(pcd_files):
                                # Adjust loop counter
                                skip_count = skip_to - i - 2  # -2 because loop will increment
                                if skip_count > 0:
                                    print(f"⏭️ Skipping {skip_count} frames...")
                                    for _ in range(skip_count):
                                        if i + 1 < len(pcd_files):
                                            i += 1
                            else:
                                print("Invalid frame number.")
                        except ValueError:
                            print("Invalid input. Continuing to next frame.")
                    elif choice == 'A':
                        print("🚀 Auto-play mode enabled. Playing all remaining frames...")
                        auto_play = True
                    # 'N' or any other choice continues to next frame

            except Exception as e:
                print(f"Error processing {pcd_file}: {e}")
                error_count += 1
                continue

    except KeyboardInterrupt:
        print("\n\n🛑 Keyboard interrupt received. Exiting gracefully...")
        controller.request_exit()

    # Final summary
    print(f"\n{'='*60}")
    print("🏁 PROCESSING SUMMARY")
    print(f"{'='*60}")
    print(f"Total files found: {len(pcd_files)}")
    print(f"Successfully processed: {processed_count}")
    print(f"Errors encountered: {error_count}")
    
    if controller.should_exit:
        print(f"⚠️  Program terminated by user request")
    elif processed_count > 0:
        print(f"✅ Successfully visualized {processed_count} files with occlusion level coloring")
    else:
        print(f"❌ No files were successfully processed")
        
    print("\n👋 Visualization complete! Thank you for using the tool.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 Program interrupted. Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)