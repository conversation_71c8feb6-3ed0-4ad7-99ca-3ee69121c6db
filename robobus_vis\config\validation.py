"""
Configuration validation utilities for conservative validation parameters.

This module provides functions to validate configuration parameters and ensure
they are within reasonable ranges for the conservative validation system.
"""

import logging
from typing import Dict, Any, List, Tuple

logger = logging.getLogger(__name__)


def validate_conservative_validation_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate conservative validation configuration parameters.
    
    Args:
        config: Configuration dictionary containing conservative_validation section
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    # Check if conservative_validation section exists
    if 'conservative_validation' not in config:
        return True, []  # Optional section, no validation needed
    
    cv_config = config['conservative_validation']
    
    # Validate enabled parameter
    if 'enabled' in cv_config:
        if not isinstance(cv_config['enabled'], bool):
            errors.append("conservative_validation.enabled must be a boolean")
    
    # Validate azimuth_tolerance_deg
    if 'azimuth_tolerance_deg' in cv_config:
        tolerance = cv_config['azimuth_tolerance_deg']
        if not isinstance(tolerance, (int, float)):
            errors.append("conservative_validation.azimuth_tolerance_deg must be a number")
        elif tolerance < 0 or tolerance > 45:
            errors.append("conservative_validation.azimuth_tolerance_deg must be between 0 and 45 degrees")
    
    # Validate min_depth_points
    if 'min_depth_points' in cv_config:
        min_points = cv_config['min_depth_points']
        if not isinstance(min_points, int):
            errors.append("conservative_validation.min_depth_points must be an integer")
        elif min_points < 1 or min_points > 100:
            errors.append("conservative_validation.min_depth_points must be between 1 and 100")
    
    # Validate tau_multiplier
    if 'tau_multiplier' in cv_config:
        multiplier = cv_config['tau_multiplier']
        if not isinstance(multiplier, (int, float)):
            errors.append("conservative_validation.tau_multiplier must be a number")
        elif multiplier < 0.5 or multiplier > 5.0:
            errors.append("conservative_validation.tau_multiplier must be between 0.5 and 5.0")
    
    return len(errors) == 0, errors


def validate_fov_filter_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate FOV filter configuration parameters.
    
    Args:
        config: Configuration dictionary containing fov_filter section
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    if 'fov_filter' not in config:
        return True, []  # Optional section
    
    fov_config = config['fov_filter']
    
    # Validate soft_margin_deg
    if 'soft_margin_deg' in fov_config:
        margin = fov_config['soft_margin_deg']
        if not isinstance(margin, (int, float)):
            errors.append("fov_filter.soft_margin_deg must be a number")
        elif margin < 0 or margin > 30:
            errors.append("fov_filter.soft_margin_deg must be between 0 and 30 degrees")
    
    return len(errors) == 0, errors


def validate_visibility_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate visibility configuration parameters.
    
    Args:
        config: Configuration dictionary containing visibility section
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    if 'visibility' not in config:
        errors.append("visibility section is required in configuration")
        return False, errors
    
    vis_config = config['visibility']
    
    # Validate treat_no_depth_as_visible
    if 'treat_no_depth_as_visible' in vis_config:
        if not isinstance(vis_config['treat_no_depth_as_visible'], bool):
            errors.append("visibility.treat_no_depth_as_visible must be a boolean")
    
    # Validate tau_base_m
    if 'tau_base_m' in vis_config:
        tau_base = vis_config['tau_base_m']
        if not isinstance(tau_base, (int, float)):
            errors.append("visibility.tau_base_m must be a number")
        elif tau_base < 0.1 or tau_base > 10.0:
            errors.append("visibility.tau_base_m must be between 0.1 and 10.0 meters")
    
    # Validate tau_scale_per_m
    if 'tau_scale_per_m' in vis_config:
        tau_scale = vis_config['tau_scale_per_m']
        if not isinstance(tau_scale, (int, float)):
            errors.append("visibility.tau_scale_per_m must be a number")
        elif tau_scale < 0.01 or tau_scale > 1.0:
            errors.append("visibility.tau_scale_per_m must be between 0.01 and 1.0")
    
    return len(errors) == 0, errors


def validate_full_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate the entire configuration for conservative validation compatibility.
    
    Args:
        config: Full configuration dictionary
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    all_errors = []
    
    # Validate each section
    is_valid_cv, cv_errors = validate_conservative_validation_config(config)
    all_errors.extend(cv_errors)
    
    is_valid_fov, fov_errors = validate_fov_filter_config(config)
    all_errors.extend(fov_errors)
    
    is_valid_vis, vis_errors = validate_visibility_config(config)
    all_errors.extend(vis_errors)
    
    # Check for configuration conflicts
    if config.get('conservative_validation', {}).get('enabled', False):
        # When conservative validation is enabled, ensure compatible settings
        if not config.get('visibility', {}).get('treat_no_depth_as_visible', False):
            logger.warning(
                "Conservative validation is enabled but treat_no_depth_as_visible is False. "
                "This may lead to overly aggressive occlusion detection."
            )
    
    return len(all_errors) == 0, all_errors


def get_safe_config_value(config: Dict[str, Any], key_path: str, default_value: Any) -> Any:
    """
    Safely retrieve a configuration value with fallback to default.
    
    Args:
        config: Configuration dictionary
        key_path: Dot-separated path to the configuration key (e.g., 'conservative_validation.enabled')
        default_value: Default value to return if key is not found
        
    Returns:
        Configuration value or default value
    """
    keys = key_path.split('.')
    current = config
    
    try:
        for key in keys:
            current = current[key]
        return current
    except (KeyError, TypeError):
        logger.debug(f"Configuration key '{key_path}' not found, using default: {default_value}")
        return default_value


def apply_conservative_defaults(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Apply conservative validation defaults to configuration if not present.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configuration dictionary with conservative defaults applied
    """
    # Ensure conservative_validation section exists
    if 'conservative_validation' not in config:
        config['conservative_validation'] = {}
    
    cv_config = config['conservative_validation']
    
    # Apply defaults
    cv_config.setdefault('enabled', False)
    cv_config.setdefault('azimuth_tolerance_deg', 10.0)
    cv_config.setdefault('min_depth_points', 5)
    cv_config.setdefault('tau_multiplier', 1.5)
    
    # Ensure fov_filter section exists and has conservative defaults
    if 'fov_filter' not in config:
        config['fov_filter'] = {}
    
    config['fov_filter'].setdefault('soft_margin_deg', 10.0)
    
    # Ensure visibility section has conservative defaults
    if 'visibility' not in config:
        config['visibility'] = {}
    
    config['visibility'].setdefault('treat_no_depth_as_visible', True)
    
    return config