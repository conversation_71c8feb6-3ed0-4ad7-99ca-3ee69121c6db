#!/usr/bin/env python3
"""
Simple test to validate ego sector corrections are working.
"""

import yaml
import numpy as np
from pathlib import Path
from robobus_vis.config.camera_correction import create_camera_corrector

def load_test_config():
    """Load the configuration with ego sector corrections."""
    config_path = Path(__file__).resolve().parent / 'configs' / 'default.yaml'
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def test_corrections_applied():
    """Test that corrections are being applied correctly."""
    print("Testing Ego Sector Corrections")
    print("=" * 50)
    
    config = load_test_config()
    ego_corrector = create_camera_corrector(config)
    
    if not ego_corrector.enabled:
        print("❌ Ego sector correction is not enabled")
        return False
    
    print("✅ Ego sector correction is enabled")
    
    # Get original and corrected maps
    original_map = config.get('ego_sector_map_deg', {})
    corrected_map = ego_corrector.get_corrected_ego_sectors_map()
    
    print(f"\nOriginal ego sectors: {len(original_map)} cameras")
    print(f"Corrected ego sectors: {len(corrected_map)} cameras")
    
    # Check specific corrections
    corrections_expected = {
        '120_left': {'original_center': 90.0, 'corrected_center': 135.0},
        'left_back': {'original_center': 150.0, 'corrected_center': 45.0},
        '120_right': {'original_center': -90.0, 'corrected_center': -135.0},
        'right_back': {'original_center': -150.0, 'corrected_center': -45.0}
    }
    
    all_correct = True
    
    for camera, expected in corrections_expected.items():
        print(f"\n📷 Testing {camera}:")
        
        # Check original
        if camera in original_map:
            orig_center = original_map[camera]['center']
            print(f"   Original center: {orig_center}°")
            if orig_center != expected['original_center']:
                print(f"   ⚠️  Expected original center {expected['original_center']}°, got {orig_center}°")
        else:
            print(f"   ❌ Camera {camera} not found in original map")
            all_correct = False
            continue
        
        # Check corrected
        if camera in corrected_map:
            corr_center = corrected_map[camera]['center']
            print(f"   Corrected center: {corr_center}°")
            if corr_center == expected['corrected_center']:
                print(f"   ✅ Correction applied correctly")
            else:
                print(f"   ❌ Expected corrected center {expected['corrected_center']}°, got {corr_center}°")
                all_correct = False
        else:
            print(f"   ❌ Camera {camera} not found in corrected map")
            all_correct = False
    
    return all_correct

def test_angle_calculations():
    """Test angle calculations for specific positions."""
    print("\n" + "=" * 50)
    print("Testing Angle Calculations")
    print("=" * 50)
    
    # Test positions and their expected angles
    test_positions = [
        {'pos': (0.0, 10.0), 'name': 'Front', 'expected_angle': 0.0},      # Straight ahead
        {'pos': (10.0, 0.0), 'name': 'Right', 'expected_angle': -90.0},    # Right side
        {'pos': (0.0, -10.0), 'name': 'Rear', 'expected_angle': 180.0},    # Behind
        {'pos': (-10.0, 0.0), 'name': 'Left', 'expected_angle': 90.0},     # Left side
        {'pos': (7.07, 7.07), 'name': 'Front-Right', 'expected_angle': -45.0},  # 45° to front-right
        {'pos': (-7.07, 7.07), 'name': 'Front-Left', 'expected_angle': 45.0},   # 45° to front-left
        {'pos': (-7.07, -7.07), 'name': 'Rear-Left', 'expected_angle': 135.0},  # 135° to rear-left
        {'pos': (7.07, -7.07), 'name': 'Rear-Right', 'expected_angle': -135.0}, # -135° to rear-right
    ]
    
    for test_pos in test_positions:
        x, y = test_pos['pos']
        calculated_angle = np.arctan2(y, x) * 180.0 / np.pi
        expected_angle = test_pos['expected_angle']
        
        print(f"{test_pos['name']:12} ({x:5.1f}, {y:5.1f}): {calculated_angle:6.1f}° (expected {expected_angle:6.1f}°)")
        
        # Handle angle wrapping for comparison
        angle_diff = abs(calculated_angle - expected_angle)
        if angle_diff > 180:
            angle_diff = 360 - angle_diff
        
        if angle_diff < 1.0:  # Within 1 degree
            print(f"             ✅ Correct")
        else:
            print(f"             ❌ Error: {angle_diff:.1f}° difference")

def test_sector_coverage():
    """Test which cameras should see objects at different positions."""
    print("\n" + "=" * 50)
    print("Testing Camera Sector Coverage")
    print("=" * 50)
    
    config = load_test_config()
    ego_corrector = create_camera_corrector(config)
    corrected_map = ego_corrector.get_corrected_ego_sectors_map()
    
    # Test positions
    test_positions = [
        {'pos': (0.0, 15.0), 'name': 'Far Front', 'angle': 0.0},
        {'pos': (0.0, -15.0), 'name': 'Far Rear', 'angle': 180.0},
        {'pos': (-15.0, 0.0), 'name': 'Far Left', 'angle': 90.0},
        {'pos': (15.0, 0.0), 'name': 'Far Right', 'angle': -90.0},
    ]
    
    for test_pos in test_positions:
        x, y = test_pos['pos']
        obj_angle = np.arctan2(y, x) * 180.0 / np.pi
        
        print(f"\n🎯 Object at {test_pos['name']} ({x}, {y}) - Angle: {obj_angle:.1f}°")
        
        visible_cameras = []
        for cam_name, sector in corrected_map.items():
            center = sector['center']
            half_width = sector['half']
            
            # Calculate angle difference
            angle_diff = obj_angle - center
            # Normalize to [-180, 180]
            while angle_diff > 180:
                angle_diff -= 360
            while angle_diff < -180:
                angle_diff += 360
            
            if abs(angle_diff) <= half_width:
                visible_cameras.append(cam_name)
                print(f"   ✅ {cam_name:12} (center: {center:6.1f}°, half: {half_width:4.1f}°, diff: {angle_diff:6.1f}°)")
            else:
                print(f"   ❌ {cam_name:12} (center: {center:6.1f}°, half: {half_width:4.1f}°, diff: {angle_diff:6.1f}°)")
        
        print(f"   Visible in {len(visible_cameras)} cameras: {visible_cameras}")

if __name__ == "__main__":
    print("Ego Sector Correction Validation")
    print("=" * 60)
    
    try:
        # Test that corrections are applied
        corrections_ok = test_corrections_applied()
        
        # Test angle calculations
        test_angle_calculations()
        
        # Test sector coverage
        test_sector_coverage()
        
        print("\n" + "=" * 60)
        if corrections_ok:
            print("✅ Ego sector corrections are working correctly!")
        else:
            print("❌ Some corrections are not working as expected")
        
        print("\n💡 The corrections should improve visibility calculations by:")
        print("   - 120_left now covers rear-left area (135°) instead of left side (90°)")
        print("   - left_back now covers front-left area (45°) instead of rear-left (150°)")
        print("   - 120_right now covers rear-right area (-135°) instead of right side (-90°)")
        print("   - right_back now covers front-right area (-45°) instead of rear-right (-150°)")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()