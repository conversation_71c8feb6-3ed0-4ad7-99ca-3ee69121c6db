# Occlusion Handling Project 架构设计文档

## 1. 项目概述

### 1.1 项目目标
专注于自动驾驶感知系统的多相机纯视觉感知可见性/遮挡标注，为3D目标检测框提供准确的遮挡属性标注。

### 1.2 核心功能
- 计算3D目标在各相机图像投影上的遮挡程度
- 进行多视角相机目标投影遮挡属性的综合判断
- 输出1-4级量化遮挡程度（1级：无遮挡，2级：轻微遮挡，3级：严重遮挡，4级：完全遮挡）
- 明确表示目标在哪些视角相机图像中出现

### 1.3 简化原则
- 彻底移除 `pose_loader` 模块及相关内容
- 去除所有与核心任务无关的模块（training、visualization等）
- 聚焦核心算法：球面投影、多相机融合、遮挡级别映射

## 2. 架构设计

### 2.1 整体架构

```
occlusion_handling_project/
├── core/                    # 核心算法模块
│   ├── __init__.py
│   ├── sphere_projection.py # 球面投影算法
│   ├── fusion.py           # 多相机融合算法
│   └── dispatcher.py       # 统一调度接口
├── geometry/               # 几何计算模块
│   ├── __init__.py
│   ├── box3d.py           # 3D框几何操作
│   └── depth_tools.py     # 深度处理工具
├── data/                   # 数据处理模块
│   ├── __init__.py
│   ├── dataset_loader.py  # 简化的数据集加载器
│   └── calib.py          # 相机标定参数处理
├── config/                 # 配置管理
│   ├── __init__.py
│   └── settings.py       # 配置参数管理
├── pipeline/              # 处理流水线
│   ├── __init__.py
│   └── occlusion_annotator.py # 遮挡标注主流水线
├── utils/                 # 通用工具
│   ├── __init__.py
│   └── common.py         # 通用函数
├── tests/                 # 测试模块
├── examples/              # 使用示例
├── config.yaml           # 默认配置文件
├── requirements.txt       # 依赖包列表
└── README.md             # 项目说明
```

### 2.2 数据流架构

```
输入数据:
├── 3D目标检测框 (Box3D)
├── 多相机图像 (Multi-camera Images)
└── 相机标定参数 (Camera Calibration)

处理流程:
1. 几何投影 → 将3D框投影到各相机图像平面
2. 可见性计算 → 使用sphere_projection算法计算每个相机视角的可见性分数
3. 多相机融合 → 使用fusion模块整合多视角结果
4. 遮挡级别映射 → 将可见性分数映射到1-4级遮挡程度

输出结果:
├── 原始3D检测框
├── 遮挡属性 (1-4级)
├── 各相机视角出现情况
└── 各相机视角遮挡程度量化
```

## 3. 核心模块设计

### 3.1 Core模块

#### 3.1.1 sphere_projection.py
- **功能**: 基于球面投影的3D对象可见性估计
- **核心函数**: `visibility_via_sphere_projection()`
- **算法**: 分析投影点周围圆形区域的深度分布判断可见性

#### 3.1.2 fusion.py
- **功能**: 多相机可见性融合算法
- **核心函数**: 
  - `fuse_camera_visibility()`: 多相机融合
  - `get_primary_visibility()`: 获取主要可见性分数
  - `to_occlusion_level()`: 可见性分数到遮挡级别映射
- **策略**: 支持保守融合（减少假阴性）和传统平均融合

#### 3.1.3 dispatcher.py
- **功能**: 可见性计算的统一调度接口
- **核心函数**: `compute_visibility()`
- **特性**: 整合球面投影和保守验证，提供统一接口

### 3.2 Geometry模块

#### 3.2.1 box3d.py
- **功能**: 3D边界框几何操作
- **核心功能**: 3D框投影、变换、几何计算

#### 3.2.2 depth_tools.py
- **功能**: 深度图处理工具
- **核心功能**: 深度数据处理、缺失值处理

### 3.3 Data模块

#### 3.3.1 dataset_loader.py
- **功能**: 简化的数据集加载器
- **特性**: 移除pose_loader相关功能，专注于必要的数据加载

#### 3.3.2 calib.py
- **功能**: 相机标定参数处理
- **核心功能**: 内外参数管理、坐标变换

### 3.4 Pipeline模块

#### 3.4.1 occlusion_annotator.py
- **功能**: 遮挡标注主流水线
- **核心类**: `OcclusionAnnotator`
- **主要方法**: `annotate_objects(objects_3d, camera_images, calib_params)`

## 4. 接口设计

### 4.1 主要接口类

#### 4.1.1 OcclusionAnnotator
```python
class OcclusionAnnotator:
    def __init__(self, config: ConfigManager)
    def annotate_objects(self, objects_3d: List[Box3D], 
                        camera_images: Dict[str, np.ndarray],
                        calib_params: Dict[str, CameraCalib]) -> List[AnnotatedObject]
```

#### 4.1.2 VisibilityCalculator
```python
class VisibilityCalculator:
    def __init__(self, config: dict)
    def calculate_visibility(self, obj_3d: Box3D, 
                           camera_data: CameraData) -> CameraVisibility
```

### 4.2 数据结构

#### 4.2.1 AnnotatedObject
```python
@dataclass
class AnnotatedObject:
    original_box: Box3D
    occlusion_level: int  # 1-4级
    camera_appearances: Dict[str, bool]  # 各相机出现情况
    camera_occlusions: Dict[str, float]  # 各相机遮挡程度
    fusion_stats: Dict[str, float]  # 融合统计信息
```

#### 4.2.2 CameraVisibility
```python
@dataclass
class CameraVisibility:
    camera_id: str
    visibility_score: float
    occlusion_level: int
    stats: Dict[str, Any]
```

## 5. 配置管理

### 5.1 配置文件结构 (config.yaml)
```yaml
algorithm:
  sphere_projection:
    pixel_radius_factor: 1.0
    tolerance_threshold: 0.1
    min_depth_points: 10
  
  fusion:
    strategy: "conservative"  # or "traditional"
    weight_method: "uniform"
  
  occlusion_mapping:
    level_1_threshold: 0.8  # 无遮挡
    level_2_threshold: 0.6  # 轻微遮挡
    level_3_threshold: 0.3  # 严重遮挡
    # level_4: < 0.3        # 完全遮挡

data:
  image_size: [1920, 1080]
  depth_processing:
    fill_missing: true
    interpolation_method: "linear"

output:
  format: "json"  # or "pickle"
  include_stats: true
  save_intermediate: false

performance:
  parallel_processing: true
  max_workers: 4
  memory_limit_gb: 8
```

### 5.2 ConfigManager类
```python
class ConfigManager:
    def __init__(self, config_path: str = None)
    def get(self, key: str, default=None)
    def update(self, updates: dict)
    def validate(self) -> bool
```

## 6. 错误处理和日志

### 6.1 自定义异常
```python
class OcclusionCalculationError(Exception): pass
class GeometryError(Exception): pass
class ConfigError(Exception): pass
class DataValidationError(Exception): pass
```

### 6.2 日志策略
- 使用Python logging模块
- 设置不同级别：DEBUG、INFO、WARNING、ERROR
- 记录算法执行过程、性能指标、异常情况
- 支持文件和控制台输出

### 6.3 数据验证
- 输入数据格式验证
- 相机参数有效性检查
- 3D框合理性验证
- 深度图完整性检查

## 7. 性能优化

### 7.1 计算优化
- 并行处理多相机数据
- 向量化计算优化
- 内存使用优化
- 缓存机制

### 7.2 监控指标
- 计算时间统计
- 内存使用监控
- 算法准确性指标
- 吞吐量统计

## 8. 测试策略

### 8.1 单元测试
- 核心算法函数测试
- 几何计算精度测试
- 配置管理测试
- 异常处理测试

### 8.2 集成测试
- 端到端流水线测试
- 多相机数据处理测试
- 性能基准测试
- 边界条件测试

## 9. 部署和使用

### 9.1 安装要求
- Python 3.8+
- NumPy, OpenCV, PyTorch
- 其他依赖见requirements.txt

### 9.2 使用示例
```python
from occlusion_handling_project import OcclusionAnnotator, ConfigManager

# 初始化
config = ConfigManager("config.yaml")
annotator = OcclusionAnnotator(config)

# 执行标注
results = annotator.annotate_objects(
    objects_3d=detection_boxes,
    camera_images=camera_data,
    calib_params=calibration_data
)

# 处理结果
for result in results:
    print(f"Object {result.original_box.id}: Level {result.occlusion_level}")
    print(f"Camera appearances: {result.camera_appearances}")
    print(f"Camera occlusions: {result.camera_occlusions}")
```

## 10. 总结

本架构设计完全满足用户要求：
1. ✅ 彻底移除pose_loader模块
2. ✅ 简化项目复杂度，专注核心任务
3. ✅ 输出1-4级量化遮挡程度
4. ✅ 明确各相机视角出现情况
5. ✅ 准确计算各相机遮挡程度（首要任务）
6. ✅ 多视角综合判断（次要任务）
7. ✅ 去除与核心任务无关的内容

架构特点：
- **模块化设计**: 清晰的模块划分，便于维护和扩展
- **低耦合高内聚**: 模块间依赖关系简单明确
- **配置驱动**: 灵活的参数配置和算法策略选择
- **健壮性**: 完善的错误处理和数据验证
- **可测试性**: 支持单元测试和集成测试
- **性能优化**: 并行处理和计算优化

该架构为后续的代码重构和实现提供了清晰的指导方向。