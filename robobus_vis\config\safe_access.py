"""
Safe configuration access utilities for conservative validation parameters.

This module provides utility functions for safely retrieving configuration values
with proper defaults and validation. It implements graceful degradation when
configuration parameters are missing or invalid.
"""

import logging
from typing import Dict, Any, Union, Optional, List, Tuple

logger = logging.getLogger(__name__)


class ConfigAccessError(Exception):
    """Exception raised when configuration access fails critically."""
    pass


def get_safe_config_value(
    config: Dict[str, Any], 
    key_path: str, 
    default_value: Any,
    value_type: Optional[type] = None,
    validator: Optional[callable] = None
) -> Any:
    """
    Safely retrieve a configuration value with fallback to default.
    
    Args:
        config: Configuration dictionary
        key_path: Dot-separated path to the configuration key (e.g., 'conservative_validation.enabled')
        default_value: Default value to return if key is not found or invalid
        value_type: Expected type of the value (optional validation)
        validator: Optional validation function that returns (is_valid, error_message)
        
    Returns:
        Configuration value or default value
        
    Example:
        enabled = get_safe_config_value(config, 'conservative_validation.enabled', False, bool)
        tolerance = get_safe_config_value(config, 'conservative_validation.azimuth_tolerance_deg', 10.0, float, 
                                        lambda x: (0 <= x <= 45, "Must be between 0 and 45"))
    """
    keys = key_path.split('.')
    current = config
    
    try:
        # Navigate through nested dictionary
        for key in keys:
            current = current[key]
        
        # Type validation if specified
        if value_type is not None and not isinstance(current, value_type):
            logger.warning(f"Configuration key '{key_path}' has wrong type. Expected {value_type.__name__}, "
                         f"got {type(current).__name__}. Using default: {default_value}")
            return default_value
        
        # Custom validation if specified
        if validator is not None:
            is_valid, error_msg = validator(current)
            if not is_valid:
                logger.warning(f"Configuration key '{key_path}' validation failed: {error_msg}. "
                             f"Using default: {default_value}")
                return default_value
        
        return current
        
    except (KeyError, TypeError, AttributeError):
        logger.debug(f"Configuration key '{key_path}' not found, using default: {default_value}")
        return default_value


def get_conservative_validation_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract conservative validation configuration with safe defaults.
    
    Args:
        config: Full configuration dictionary
        
    Returns:
        Conservative validation configuration with all required parameters
    """
    def validate_tolerance(value):
        return (0 <= value <= 45, "Azimuth tolerance must be between 0 and 45 degrees")
    
    def validate_min_points(value):
        return (1 <= value <= 100, "Min depth points must be between 1 and 100")
    
    def validate_tau_multiplier(value):
        return (0.5 <= value <= 5.0, "Tau multiplier must be between 0.5 and 5.0")
    
    return {
        'enabled': get_safe_config_value(config, 'conservative_validation.enabled', False, bool),
        'azimuth_tolerance_deg': get_safe_config_value(
            config, 'conservative_validation.azimuth_tolerance_deg', 10.0, (int, float), validate_tolerance
        ),
        'min_depth_points': get_safe_config_value(
            config, 'conservative_validation.min_depth_points', 5, int, validate_min_points
        ),
        'tau_multiplier': get_safe_config_value(
            config, 'conservative_validation.tau_multiplier', 1.5, (int, float), validate_tau_multiplier
        )
    }


def get_visibility_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract visibility configuration with safe defaults.
    
    Args:
        config: Full configuration dictionary
        
    Returns:
        Visibility configuration with all required parameters
    """
    def validate_tau_base(value):
        return (0.1 <= value <= 10.0, "Tau base must be between 0.1 and 10.0 meters")
    
    def validate_tau_scale(value):
        return (0.01 <= value <= 1.0, "Tau scale must be between 0.01 and 1.0")
    
    def validate_sphere_radius(value):
        return (0.05 <= value <= 1.0, "Sphere radius must be between 0.05 and 1.0 meters")
    
    def validate_occlusion_threshold(value):
        return (0.0 <= value <= 1.0, "Occlusion threshold must be between 0.0 and 1.0")
    
    return {
        'tau_base_m': get_safe_config_value(
            config, 'visibility.tau_base_m', 2.0, (int, float), validate_tau_base
        ),
        'tau_scale_per_m': get_safe_config_value(
            config, 'visibility.tau_scale_per_m', 0.1, (int, float), validate_tau_scale
        ),
        'sphere_radius_m': get_safe_config_value(
            config, 'visibility.sphere_radius_m', 0.15, (int, float), validate_sphere_radius
        ),
        'occlusion_fraction_thr': get_safe_config_value(
            config, 'visibility.occlusion_fraction_thr', 0.2, (int, float), validate_occlusion_threshold
        ),
        'min_neighbors': get_safe_config_value(config, 'visibility.min_neighbors', 3, int),
        'treat_no_depth_as_visible': get_safe_config_value(
            config, 'visibility.treat_no_depth_as_visible', True, bool
        ),
        'max_window_px': get_safe_config_value(config, 'visibility.max_window_px', 15, int),
        'enable_diagnostics': get_safe_config_value(config, 'visibility.enable_diagnostics', False, bool),
        'diagnostic_output_dir': get_safe_config_value(
            config, 'visibility.diagnostic_output_dir', 'outputs/diagnostics', str
        )
    }


def get_fov_filter_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract FOV filter configuration with safe defaults.
    
    Args:
        config: Full configuration dictionary
        
    Returns:
        FOV filter configuration with all required parameters
    """
    def validate_soft_margin(value):
        return (0 <= value <= 30, "Soft margin must be between 0 and 30 degrees")
    
    def validate_vfov(value):
        return (10 <= value <= 180, "Vertical FOV must be between 10 and 180 degrees")
    
    return {
        'enabled': get_safe_config_value(config, 'fov_filter.enabled', True, bool),
        'use_corner_check': get_safe_config_value(config, 'fov_filter.use_corner_check', False, bool),
        'vfov_deg_default': get_safe_config_value(
            config, 'fov_filter.vfov_deg_default', 50.0, (int, float), validate_vfov
        ),
        'debug_filtering': get_safe_config_value(config, 'fov_filter.debug_filtering', False, bool),
        'fallback_on_filter': get_safe_config_value(config, 'fov_filter.fallback_on_filter', True, bool),
        'soft_margin_deg': get_safe_config_value(
            config, 'fov_filter.soft_margin_deg', 10.0, (int, float), validate_soft_margin
        )
    }


def get_camera_config(config: Dict[str, Any], camera_name: str) -> Dict[str, Any]:
    """
    Get camera-specific configuration with overrides and safe defaults.
    
    Args:
        config: Full configuration dictionary
        camera_name: Name of the camera
        
    Returns:
        Camera-specific configuration with overrides applied
    """
    # Get base visibility configuration
    base_vis_config = get_visibility_config(config)
    
    # Apply camera-specific overrides if they exist
    overrides = get_safe_config_value(config, f'visibility_overrides.{camera_name}', {}, dict)
    
    # Merge overrides with base configuration
    camera_config = base_vis_config.copy()
    for key, value in overrides.items():
        if key in camera_config:
            camera_config[key] = value
            logger.debug(f"Applied override for {camera_name}.{key}: {value}")
    
    return camera_config


def get_camera_fov_config(config: Dict[str, Any], camera_name: str) -> Dict[str, float]:
    """
    Get camera FOV configuration with safe defaults.
    
    Args:
        config: Full configuration dictionary
        camera_name: Name of the camera
        
    Returns:
        Dictionary with 'h' (horizontal) and 'v' (vertical) FOV in degrees
    """
    def validate_fov(value):
        return (10 <= value <= 180, "FOV must be between 10 and 180 degrees")
    
    # Default FOV values based on camera type
    default_fovs = {
        '60_front': {'h': 70.0, 'v': 50.0},
        '120_front': {'h': 130.0, 'v': 50.0},
        '120_back': {'h': 130.0, 'v': 50.0},
        '120_left': {'h': 130.0, 'v': 50.0},
        '120_right': {'h': 130.0, 'v': 50.0},
        'left_back': {'h': 130.0, 'v': 50.0},
        'right_back': {'h': 130.0, 'v': 50.0}
    }
    
    default_fov = default_fovs.get(camera_name, {'h': 120.0, 'v': 50.0})
    
    return {
        'h': get_safe_config_value(
            config, f'camera_fovs_deg.{camera_name}.h', default_fov['h'], (int, float), validate_fov
        ),
        'v': get_safe_config_value(
            config, f'camera_fovs_deg.{camera_name}.v', default_fov['v'], (int, float), validate_fov
        )
    }


def validate_configuration_startup(config: Dict[str, Any]) -> Tuple[bool, List[str], List[str]]:
    """
    Validate configuration at system startup with comprehensive checks.
    
    Args:
        config: Full configuration dictionary
        
    Returns:
        Tuple of (is_valid, critical_errors, warnings)
    """
    critical_errors = []
    warnings = []
    
    try:
        # Check if conservative validation is enabled and validate its config
        cv_config = get_conservative_validation_config(config)
        if cv_config['enabled']:
            # Validate conservative validation parameters
            if cv_config['azimuth_tolerance_deg'] > 30:
                warnings.append(f"Large azimuth tolerance ({cv_config['azimuth_tolerance_deg']}°) may cause performance issues")
            
            if cv_config['min_depth_points'] < 3:
                warnings.append(f"Very low min_depth_points ({cv_config['min_depth_points']}) may cause unreliable validation")
            
            if cv_config['tau_multiplier'] > 3.0:
                warnings.append(f"High tau_multiplier ({cv_config['tau_multiplier']}) may cause overly permissive occlusion detection")
        
        # Validate visibility configuration
        vis_config = get_visibility_config(config)
        if vis_config['tau_base_m'] > 5.0:
            warnings.append(f"Large tau_base_m ({vis_config['tau_base_m']}m) may cause overly permissive occlusion detection")
        
        if vis_config['sphere_radius_m'] > 0.5:
            warnings.append(f"Large sphere_radius_m ({vis_config['sphere_radius_m']}m) may cause performance issues")
        
        # Check for configuration conflicts
        if cv_config['enabled'] and not vis_config['treat_no_depth_as_visible']:
            warnings.append("Conservative validation is enabled but treat_no_depth_as_visible is False. "
                          "This may lead to overly aggressive occlusion detection.")
        
        # Validate FOV configuration
        fov_config = get_fov_filter_config(config)
        if fov_config['soft_margin_deg'] > 20:
            warnings.append(f"Large soft_margin_deg ({fov_config['soft_margin_deg']}°) may include too many out-of-FOV objects")
        
        # Check camera configurations
        camera_names = ['60_front', '120_front', '120_back', '120_left', '120_right', 'left_back', 'right_back']
        for camera_name in camera_names:
            try:
                fov_config = get_camera_fov_config(config, camera_name)
                if fov_config['h'] < 30 or fov_config['v'] < 20:
                    warnings.append(f"Very narrow FOV for {camera_name}: h={fov_config['h']}°, v={fov_config['v']}°")
            except Exception as e:
                warnings.append(f"Failed to validate FOV config for {camera_name}: {e}")
        
    except Exception as e:
        critical_errors.append(f"Configuration validation failed: {e}")
    
    is_valid = len(critical_errors) == 0
    return is_valid, critical_errors, warnings


def get_safe_nested_value(config: Dict[str, Any], keys: List[str], default: Any) -> Any:
    """
    Safely get a nested value from configuration dictionary.
    
    Args:
        config: Configuration dictionary
        keys: List of keys to traverse (e.g., ['conservative_validation', 'enabled'])
        default: Default value if key path doesn't exist
        
    Returns:
        Value at the key path or default
    """
    current = config
    try:
        for key in keys:
            current = current[key]
        return current
    except (KeyError, TypeError, AttributeError):
        return default


def ensure_config_section(config: Dict[str, Any], section_name: str, defaults: Dict[str, Any]) -> Dict[str, Any]:
    """
    Ensure a configuration section exists with default values.
    
    Args:
        config: Configuration dictionary to modify
        section_name: Name of the section to ensure
        defaults: Default values for the section
        
    Returns:
        The configuration section (existing or newly created)
    """
    if section_name not in config:
        config[section_name] = {}
        logger.debug(f"Created missing configuration section: {section_name}")
    
    section = config[section_name]
    
    # Apply defaults for missing keys
    for key, default_value in defaults.items():
        if key not in section:
            section[key] = default_value
            logger.debug(f"Applied default for {section_name}.{key}: {default_value}")
    
    return section


def create_minimal_config() -> Dict[str, Any]:
    """
    Create a minimal working configuration with safe defaults.
    
    Returns:
        Minimal configuration dictionary that allows the system to function
    """
    return {
        'visibility': {
            'tau_base_m': 2.0,
            'tau_scale_per_m': 0.1,
            'sphere_radius_m': 0.15,
            'occlusion_fraction_thr': 0.2,
            'min_neighbors': 3,
            'treat_no_depth_as_visible': True,
            'max_window_px': 15,
            'enable_diagnostics': False,
            'diagnostic_output_dir': 'outputs/diagnostics'
        },
        'conservative_validation': {
            'enabled': False,
            'azimuth_tolerance_deg': 10.0,
            'min_depth_points': 5,
            'tau_multiplier': 1.5
        },
        'fov_filter': {
            'enabled': True,
            'use_corner_check': False,
            'vfov_deg_default': 50.0,
            'debug_filtering': False,
            'fallback_on_filter': True,
            'soft_margin_deg': 10.0
        },
        'camera_fovs_deg': {
            '60_front': {'h': 70.0, 'v': 50.0},
            '120_front': {'h': 130.0, 'v': 50.0},
            '120_back': {'h': 130.0, 'v': 50.0},
            '120_left': {'h': 130.0, 'v': 50.0},
            '120_right': {'h': 130.0, 'v': 50.0},
            'left_back': {'h': 130.0, 'v': 50.0},
            'right_back': {'h': 130.0, 'v': 50.0}
        }
    }


def log_configuration_access_summary(config: Dict[str, Any]) -> None:
    """
    Log a summary of configuration access patterns for debugging.
    
    Args:
        config: Configuration dictionary
    """
    logger.info("=== Configuration Access Summary ===")
    
    # Conservative validation
    cv_config = get_conservative_validation_config(config)
    logger.info(f"Conservative Validation: {'ENABLED' if cv_config['enabled'] else 'DISABLED'}")
    if cv_config['enabled']:
        logger.info(f"  Azimuth Tolerance: {cv_config['azimuth_tolerance_deg']}°")
        logger.info(f"  Min Depth Points: {cv_config['min_depth_points']}")
        logger.info(f"  Tau Multiplier: {cv_config['tau_multiplier']}")
    
    # Visibility settings
    vis_config = get_visibility_config(config)
    logger.info(f"Visibility Settings:")
    logger.info(f"  Tau Base: {vis_config['tau_base_m']}m")
    logger.info(f"  Tau Scale: {vis_config['tau_scale_per_m']}")
    logger.info(f"  Sphere Radius: {vis_config['sphere_radius_m']}m")
    logger.info(f"  Treat No Depth as Visible: {vis_config['treat_no_depth_as_visible']}")
    
    # FOV filter
    fov_config = get_fov_filter_config(config)
    logger.info(f"FOV Filter: {'ENABLED' if fov_config['enabled'] else 'DISABLED'}")
    if fov_config['enabled']:
        logger.info(f"  Soft Margin: {fov_config['soft_margin_deg']}°")
    
    logger.info("=== End Configuration Access Summary ===")


def handle_configuration_degradation(config: Dict[str, Any], error: Exception) -> Dict[str, Any]:
    """
    Handle configuration errors with graceful degradation.
    
    Args:
        config: Original configuration (may be invalid)
        error: Exception that occurred during configuration processing
        
    Returns:
        Safe configuration that allows system to continue functioning
    """
    logger.error(f"Configuration error occurred: {error}")
    logger.warning("Implementing graceful degradation with safe defaults")
    
    # Start with minimal config
    safe_config = create_minimal_config()
    
    # Try to preserve valid parts of original config
    if isinstance(config, dict):
        try:
            # Preserve visibility settings if they're valid
            if 'visibility' in config and isinstance(config['visibility'], dict):
                vis_config = get_visibility_config(config)
                safe_config['visibility'].update(vis_config)
                logger.info("Preserved visibility configuration")
        except Exception as e:
            logger.warning(f"Could not preserve visibility config: {e}")
        
        try:
            # Preserve FOV settings if they're valid
            if 'fov_filter' in config and isinstance(config['fov_filter'], dict):
                fov_config = get_fov_filter_config(config)
                safe_config['fov_filter'].update(fov_config)
                logger.info("Preserved FOV filter configuration")
        except Exception as e:
            logger.warning(f"Could not preserve FOV config: {e}")
        
        try:
            # Disable conservative validation to ensure stability
            safe_config['conservative_validation']['enabled'] = False
            logger.info("Disabled conservative validation for stability")
        except Exception as e:
            logger.warning(f"Could not disable conservative validation: {e}")
    
    return safe_config