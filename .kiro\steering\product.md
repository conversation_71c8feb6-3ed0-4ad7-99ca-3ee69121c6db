# Product Overview

VisionAware Annotations is a visibility/occlusion annotation toolkit for multi-camera pure-vision perception on RoboBUS datasets. The project addresses the "supervision signal mismatch" problem in autonomous driving by automatically calculating visibility and occlusion attributes for 3D annotated objects.

## Core Purpose
- Transition from expensive multi-modal sensor systems (cameras + LiDAR) to cost-effective vision-only solutions
- Provide robust, configuration-driven visibility/occlusion annotations for autonomous driving datasets
- Enable downstream training/data curation with reliable occlusion levels and visibility statistics
- Solve false-positive detections in vision-only models through accurate occlusion analysis

## Key Features
- Sphere Projection Visibility algorithm for accurate occlusion detection
- Multi-camera support with per-camera visibility calculations
- Configuration-driven FOV and ego-sector gating
- Batch processing pipeline with parallel object processing
- Visualization tools for qualitative verification


## Target Users
- Autonomous driving perception engineers
- The enhanced annotations generated by this project may be used in BEVFusion (pure vision perception algorithm) and FlashOCC's occupancy network for pure vision perception algorithms.
- Computer vision researchers working on multi-camera systems
- Dataset annotation teams requiring occlusion analysis
- ML engineers training vision-only perception models