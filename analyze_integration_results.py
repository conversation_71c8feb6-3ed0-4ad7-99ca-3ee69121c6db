#!/usr/bin/env python3
"""
Analysis script for integration test results.

This script analyzes the JSON output files from the pipeline run to evaluate
the effectiveness of the safe configuration access implementation and overall
system performance.
"""

import json
import os
import sys
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict, Counter
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def load_results(results_dir):
    """Load all JSON result files from the results directory."""
    results_dir = Path(results_dir)
    json_files = list(results_dir.glob("*.json"))
    
    if not json_files:
        logger.error(f"No JSON files found in {results_dir}")
        return []
    
    results = []
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                results.append({
                    'filename': json_file.name,
                    'timestamp': json_file.stem,
                    'data': data
                })
        except Exception as e:
            logger.warning(f"Failed to load {json_file}: {e}")
    
    logger.info(f"Loaded {len(results)} result files")
    return results


def analyze_visibility_statistics(results):
    """Analyze visibility statistics across all frames."""
    logger.info("=== Visibility Statistics Analysis ===")
    
    all_objects = []
    visibility_scores = []
    occlusion_levels = []
    fusion_methods = []
    camera_coverage = []
    
    for result in results:
        objects = result['data'].get('result', {}).get('data', [])
        all_objects.extend(objects)
        
        for obj in objects:
            vis_data = obj.get('visibility', {})
            
            # Collect visibility scores
            vis_avg = vis_data.get('visibility_rate_avg', 0)
            vis_max = vis_data.get('visibility_rate_max', 0)
            visibility_scores.append({
                'avg': vis_avg,
                'max': vis_max,
                'primary': vis_data.get('visibility_rate_primary', vis_avg)
            })
            
            # Collect occlusion levels
            occlusion_levels.append(vis_data.get('occlusion_level', 4))
            
            # Collect fusion statistics
            fusion_stats = vis_data.get('fusion_stats', {})
            fusion_methods.append(fusion_stats.get('fusion_method', 'unknown'))
            
            # Analyze camera coverage
            per_cam = vis_data.get('per_camera', {})
            valid_cameras = sum(1 for v in per_cam.values() if v is not None)
            visible_cameras = len(vis_data.get('visible_in_views', []))
            camera_coverage.append({
                'total': len(per_cam),
                'valid': valid_cameras,
                'visible': visible_cameras
            })
    
    # Print statistics
    logger.info(f"Total objects analyzed: {len(all_objects)}")
    
    # Visibility score statistics
    vis_avgs = [v['avg'] for v in visibility_scores]
    vis_maxs = [v['max'] for v in visibility_scores]
    
    logger.info(f"Visibility Average - Mean: {np.mean(vis_avgs):.3f}, Std: {np.std(vis_avgs):.3f}")
    logger.info(f"Visibility Maximum - Mean: {np.mean(vis_maxs):.3f}, Std: {np.std(vis_maxs):.3f}")
    logger.info(f"Visibility Range: {np.min(vis_avgs):.3f} - {np.max(vis_maxs):.3f}")
    
    # Occlusion level distribution
    occlusion_dist = Counter(occlusion_levels)
    logger.info("Occlusion Level Distribution:")
    for level in sorted(occlusion_dist.keys()):
        count = occlusion_dist[level]
        percentage = (count / len(occlusion_levels)) * 100
        logger.info(f"  Level {level}: {count} objects ({percentage:.1f}%)")
    
    # Fusion method analysis
    fusion_dist = Counter(fusion_methods)
    logger.info("Fusion Method Distribution:")
    for method, count in fusion_dist.items():
        percentage = (count / len(fusion_methods)) * 100
        logger.info(f"  {method}: {count} objects ({percentage:.1f}%)")
    
    # Camera coverage analysis
    valid_counts = [c['valid'] for c in camera_coverage]
    visible_counts = [c['visible'] for c in camera_coverage]
    
    logger.info(f"Camera Coverage - Valid cameras per object: {np.mean(valid_counts):.1f} ± {np.std(valid_counts):.1f}")
    logger.info(f"Camera Coverage - Visible cameras per object: {np.mean(visible_counts):.1f} ± {np.std(visible_counts):.1f}")
    
    return {
        'visibility_scores': visibility_scores,
        'occlusion_levels': occlusion_levels,
        'fusion_methods': fusion_methods,
        'camera_coverage': camera_coverage
    }


def analyze_safe_config_effectiveness(results):
    """Analyze the effectiveness of safe configuration access patterns."""
    logger.info("=== Safe Configuration Access Analysis ===")
    
    # Check for configuration-related issues
    config_issues = []
    fusion_stats_analysis = defaultdict(list)
    
    for result in results:
        objects = result['data'].get('result', {}).get('data', [])
        
        for obj in objects:
            vis_data = obj.get('visibility', {})
            fusion_stats = vis_data.get('fusion_stats', {})
            
            # Analyze fusion statistics for configuration effectiveness
            fusion_method = fusion_stats.get('fusion_method', 'unknown')
            max_prioritized = fusion_stats.get('max_prioritized', False)
            conservative_threshold = fusion_stats.get('conservative_threshold', 0.15)
            
            fusion_stats_analysis['methods'].append(fusion_method)
            fusion_stats_analysis['max_prioritized'].append(max_prioritized)
            fusion_stats_analysis['thresholds'].append(conservative_threshold)
            
            # Check for potential configuration issues
            if fusion_method == 'unknown':
                config_issues.append(f"Unknown fusion method in {result['filename']}")
            
            # Check if conservative validation is working
            vis_improvement = fusion_stats.get('vis_max_improvement', 0)
            if vis_improvement > 0:
                fusion_stats_analysis['improvements'].append(vis_improvement)
    
    # Analyze fusion method consistency
    methods = fusion_stats_analysis['methods']
    method_dist = Counter(methods)
    
    logger.info("Fusion Method Consistency:")
    for method, count in method_dist.items():
        percentage = (count / len(methods)) * 100
        logger.info(f"  {method}: {count} ({percentage:.1f}%)")
    
    # Check conservative validation usage
    max_prioritized_count = sum(fusion_stats_analysis['max_prioritized'])
    total_objects = len(fusion_stats_analysis['max_prioritized'])
    
    logger.info(f"Conservative Fusion Usage: {max_prioritized_count}/{total_objects} objects ({(max_prioritized_count/total_objects)*100:.1f}%)")
    
    # Analyze threshold consistency
    thresholds = fusion_stats_analysis['thresholds']
    unique_thresholds = set(thresholds)
    logger.info(f"Conservative Threshold Values: {unique_thresholds}")
    
    # Check for improvements from conservative validation
    improvements = fusion_stats_analysis.get('improvements', [])
    if improvements:
        logger.info(f"Visibility Improvements: {len(improvements)} cases, avg improvement: {np.mean(improvements):.3f}")
    else:
        logger.info("No visibility improvements detected (conservative validation may be disabled)")
    
    # Report configuration issues
    if config_issues:
        logger.warning(f"Configuration Issues Found: {len(config_issues)}")
        for issue in config_issues[:5]:  # Show first 5 issues
            logger.warning(f"  {issue}")
    else:
        logger.info("✓ No configuration issues detected")
    
    return {
        'fusion_methods': method_dist,
        'conservative_usage': max_prioritized_count / total_objects,
        'config_issues': config_issues,
        'improvements': improvements
    }


def analyze_system_performance(results):
    """Analyze overall system performance and reliability."""
    logger.info("=== System Performance Analysis ===")
    
    frame_count = len(results)
    total_objects = 0
    processing_success_rate = 0
    
    camera_usage = defaultdict(int)
    camera_success = defaultdict(int)
    
    for result in results:
        objects = result['data'].get('result', {}).get('data', [])
        total_objects += len(objects)
        
        if objects:  # Frame processed successfully if it has objects
            processing_success_rate += 1
        
        for obj in objects:
            vis_data = obj.get('visibility', {})
            per_cam = vis_data.get('per_camera', {})
            
            for cam_name, vis_score in per_cam.items():
                camera_usage[cam_name] += 1
                if vis_score is not None:
                    camera_success[cam_name] += 1
    
    # Calculate success rates
    processing_success_rate = (processing_success_rate / frame_count) * 100
    
    logger.info(f"Frames Processed: {frame_count}")
    logger.info(f"Total Objects: {total_objects}")
    logger.info(f"Objects per Frame: {total_objects / frame_count:.1f}")
    logger.info(f"Processing Success Rate: {processing_success_rate:.1f}%")
    
    # Camera performance analysis
    logger.info("Camera Performance:")
    for cam_name in sorted(camera_usage.keys()):
        usage = camera_usage[cam_name]
        success = camera_success[cam_name]
        success_rate = (success / usage) * 100 if usage > 0 else 0
        logger.info(f"  {cam_name}: {success}/{usage} ({success_rate:.1f}% success rate)")
    
    return {
        'frame_count': frame_count,
        'total_objects': total_objects,
        'processing_success_rate': processing_success_rate,
        'camera_performance': dict(camera_success)
    }


def generate_summary_report(vis_stats, config_stats, perf_stats):
    """Generate a comprehensive summary report."""
    logger.info("=== INTEGRATION TEST SUMMARY REPORT ===")
    
    # Overall system health
    logger.info("## System Health Assessment")
    
    if perf_stats['processing_success_rate'] > 95:
        logger.info("✅ EXCELLENT: Processing success rate > 95%")
    elif perf_stats['processing_success_rate'] > 80:
        logger.info("✅ GOOD: Processing success rate > 80%")
    else:
        logger.info("⚠️  WARNING: Processing success rate < 80%")
    
    if len(config_stats['config_issues']) == 0:
        logger.info("✅ EXCELLENT: No configuration issues detected")
    else:
        logger.info(f"⚠️  WARNING: {len(config_stats['config_issues'])} configuration issues found")
    
    # Safe configuration access effectiveness
    logger.info("## Safe Configuration Access Assessment")
    
    traditional_fusion = config_stats['fusion_methods'].get('traditional_avg', 0)
    conservative_fusion = config_stats['fusion_methods'].get('conservative_max', 0)
    
    if traditional_fusion > 0:
        logger.info("✅ WORKING: Traditional fusion method functioning")
    
    if conservative_fusion > 0:
        logger.info("✅ WORKING: Conservative fusion method functioning")
        logger.info(f"   Conservative usage: {config_stats['conservative_usage']*100:.1f}%")
    else:
        logger.info("ℹ️  INFO: Conservative validation appears to be disabled (expected with current config)")
    
    # Visibility calculation quality
    logger.info("## Visibility Calculation Quality")
    
    vis_scores = vis_stats['visibility_scores']
    avg_visibility = np.mean([v['avg'] for v in vis_scores])
    
    if avg_visibility > 0.1:
        logger.info(f"✅ GOOD: Average visibility score {avg_visibility:.3f} indicates reasonable detection")
    else:
        logger.info(f"⚠️  LOW: Average visibility score {avg_visibility:.3f} may indicate over-conservative detection")
    
    # Occlusion level distribution assessment
    occlusion_dist = Counter(vis_stats['occlusion_levels'])
    level_1_percentage = (occlusion_dist.get(1, 0) / len(vis_stats['occlusion_levels'])) * 100
    
    if level_1_percentage > 50:
        logger.info(f"✅ GOOD: {level_1_percentage:.1f}% of objects at occlusion level 1 (visible)")
    else:
        logger.info(f"ℹ️  INFO: {level_1_percentage:.1f}% of objects at occlusion level 1")
    
    # Final assessment
    logger.info("## Final Assessment")
    
    issues = []
    if perf_stats['processing_success_rate'] < 95:
        issues.append("Processing success rate could be improved")
    if len(config_stats['config_issues']) > 0:
        issues.append("Configuration issues detected")
    if avg_visibility < 0.05:
        issues.append("Very low visibility scores detected")
    
    if not issues:
        logger.info("🎉 EXCELLENT: All systems functioning optimally!")
        logger.info("   ✅ Safe configuration access working correctly")
        logger.info("   ✅ Pipeline processing successfully")
        logger.info("   ✅ Visibility calculations producing reasonable results")
        return True
    else:
        logger.info("⚠️  ISSUES DETECTED:")
        for issue in issues:
            logger.info(f"   - {issue}")
        return False


def main():
    """Main analysis function."""
    if len(sys.argv) != 2:
        print("Usage: python analyze_integration_results.py <results_directory>")
        sys.exit(1)
    
    results_dir = sys.argv[1]
    
    if not os.path.exists(results_dir):
        logger.error(f"Results directory not found: {results_dir}")
        sys.exit(1)
    
    # Load results
    results = load_results(results_dir)
    if not results:
        logger.error("No results to analyze")
        sys.exit(1)
    
    # Perform analyses
    vis_stats = analyze_visibility_statistics(results)
    config_stats = analyze_safe_config_effectiveness(results)
    perf_stats = analyze_system_performance(results)
    
    # Generate summary report
    success = generate_summary_report(vis_stats, config_stats, perf_stats)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()