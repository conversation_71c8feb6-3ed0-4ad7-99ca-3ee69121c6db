# Project Structure

## Root Directory Layout
```
├── robobus_vis/           # Main package - core visibility algorithms
├── configs/               # Configuration files (YAML)
├── docs/                  # Algorithm documentation
├── tests/                 # Unit and integration tests
├── tools/                 # Utility scripts and demos
├── visibility_demo_data/  # Sample datasets for testing
├── outputs/               # Generated results and visualizations
├── .history/              # Version history tracking
└── *.py                   # Standalone scripts and tools
```

## Core Package Architecture (robobus_vis/)
```
robobus_vis/
├── calib/          # Camera calibration and models
├── config/         # Configuration utilities
├── geometry/       # 3D geometry operations
├── io/             # Data loading (datasets, calibration, pose)
├── pipeline/       # Main processing pipelines
├── tools/          # Package-specific utilities
├── training/       # Training-related functionality
├── vis/            # Visualization helpers and exporters
└── visibility/     # Core visibility algorithms
```

## Key Module Responsibilities

### Core Algorithm Modules
- `visibility/sphere_projection.py` - Main sphere projection algorithm
- `visibility/dispatcher.py` - Visibility computation orchestration
- `visibility/fusion.py` - Multi-camera visibility fusion
- `visibility/fov_filter.py` - FOV and ego-sector gating

### Data Processing
- `io/dataset_loader.py` - RoboBUS dataset loading
- `io/calib_parser.py` - Camera calibration parsing
- `io/pose_loader.py` - Optional SE(3) motion compensation
- `calib/camera_model.py` - Camera projection models

### Pipeline Entry Points
- `pipeline/run_batch.py` - Main batch processing pipeline
- `pipeline/run_clip.py` - Single clip inspection utility

### Visualization
- `vis/draw2d.py` - 2D drawing utilities
- `vis/exporters.py` - Image and result export functions
- `batch_visualization.py` - Standalone batch visualization tool

## Configuration System
- `configs/default.yaml` - Central configuration file
- Hierarchical parameter structure: visibility, fov_filter, camera_fovs_deg, etc.
- Per-camera parameter overrides supported
- Safe configuration access patterns to avoid KeyErrors

## File Naming Conventions
- **Snake_case** for Python files and directories
- **Descriptive names** reflecting functionality
- **Module prefixes** for related functionality (e.g., `test_*.py` for tests)
- **Timestamp suffixes** for generated outputs and history files

## Data Flow Patterns
1. **Input**: RoboBUS clip directory (images, point clouds, calibration, annotations)
2. **Processing**: Per-frame depth map generation → per-object visibility computation → multi-camera fusion
3. **Output**: Augmented JSON with visibility attributes + optional visualizations

## Development Patterns
- **Modular design** with clear separation of concerns
- **Configuration-driven** behavior via YAML files
- **Logging-instrumented** for performance monitoring and debugging
- **Parallel processing** using ThreadPoolExecutor for scalability
- **Diagnostic outputs** for algorithm validation and tuning