#!/usr/bin/env python3
"""
LiDAR-Enhanced BEV Visualization Script for Occlusion-Aware 3D Object Detection

This script extends the original BEV visualization to overlay LiDAR point clouds
with 3D object detection boxes, providing enhanced spatial context for occlusion analysis.

Key Features:
- Black background with white LiDAR point cloud rendering
- Zoomed coordinate ranges: X[-80,+80m], Y[-50,+50m] for detailed inspection
- Occlusion-based color coding for detection boxes (Green/Yellow/Red/Gray)
- Open3D integration for PCD file loading
- Performance optimizations for dense point clouds

Author: TRAE AI Agent
Date: 2025-01-25
Version: 1.0
"""

import os
import json
import logging
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any

import cv2
import numpy as np
import open3d as o3d
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiDARBEVVisualizationProcessor:
    """
    Enhanced BEV visualization processor with LiDAR point cloud integration.
    
    Extends the original BatchBEVVisualizationProcessor with:
    - LiDAR point cloud loading and rendering
    - Zoomed coordinate system for detailed inspection
    - Black background with white point cloud visualization
    - Maintained occlusion-based box color coding
    """
    
    def __init__(
        self,
        # TRAE-MOD START [20250125-1400-coord-system]: Purpose - implement zoomed coordinate ranges
        x_range: Tuple[float, float] = (-80.0, 80.0),  # Changed from (-180, 180)
        y_range: Tuple[float, float] = (-50.0, 50.0),  # Changed from (-100, 100)
        pixels_per_m: float = 10.0,  # Increased from 5.0 for better resolution
        # TRAE-MOD END [20250125-1400-coord-system]
        pcd_dir: Optional[str] = None,
        max_points_render: int = 50000,  # Performance optimization
        point_size: int = 1
    ):
        """
        Initialize the LiDAR BEV visualization processor.
        
        Args:
            x_range: X-axis coordinate range in meters (forward direction)
            y_range: Y-axis coordinate range in meters (left direction)
            pixels_per_m: Pixel density per meter for image resolution
            pcd_dir: Directory containing PCD files (optional)
            max_points_render: Maximum points to render for performance
            point_size: Size of rendered points in pixels
        """
        self.x_range = x_range
        self.y_range = y_range
        self.pixels_per_m = pixels_per_m
        self.pcd_dir = pcd_dir
        self.max_points_render = max_points_render
        self.point_size = point_size
        
        # TRAE-MOD START [20250125-1400-image-dims]: Purpose - calculate image dimensions for new coordinate ranges
        # Calculate image dimensions based on coordinate ranges
        self.x_span = x_range[1] - x_range[0]  # 160m
        self.y_span = y_range[1] - y_range[0]  # 100m
        
        # Image dimensions: height corresponds to X (forward), width to Y (left)
        self.img_height = int(self.x_span * pixels_per_m)  # 1600 pixels
        self.img_width = int(self.y_span * pixels_per_m)   # 1000 pixels
        # TRAE-MOD END [20250125-1400-image-dims]
        
        logger.info(f"Initialized LiDAR BEV processor:")
        logger.info(f"  Coordinate ranges: X{x_range}, Y{y_range}")
        logger.info(f"  Image dimensions: {self.img_height}x{self.img_width}")
        logger.info(f"  Pixels per meter: {pixels_per_m}")
        if pcd_dir:
            logger.info(f"  PCD directory: {pcd_dir}")
    
    def _world_to_pixel(self, x: float, y: float) -> Tuple[int, int]:
        """
        Convert world coordinates to pixel coordinates with clipping.
        
        Args:
            x: X coordinate in meters (forward direction)
            y: Y coordinate in meters (left direction)
            
        Returns:
            Tuple of (pixel_x, pixel_y) coordinates
        """
        # TRAE-MOD START [20250125-1400-coord-transform]: Purpose - implement new coordinate transformation with clipping
        # Clip coordinates to valid ranges
        x_clipped = np.clip(x, self.x_range[0], self.x_range[1])
        y_clipped = np.clip(y, self.y_range[0], self.y_range[1])
        
        # Transform to pixel coordinates
        # X (forward) maps to image height (vertical axis)
        # Y (left) maps to image width (horizontal axis)
        pixel_y = int((x_clipped - self.x_range[0]) * self.pixels_per_m)
        pixel_x = int((y_clipped - self.y_range[0]) * self.pixels_per_m)
        
        # Ensure pixels are within image bounds
        pixel_x = np.clip(pixel_x, 0, self.img_width - 1)
        pixel_y = np.clip(pixel_y, 0, self.img_height - 1)
        
        return pixel_x, pixel_y
        # TRAE-MOD END [20250125-1400-coord-transform]
    
    def _load_json_data(self, json_path: str) -> List[Dict[str, Any]]:
        """
        Load and parse JSON annotation data.
        
        Args:
            json_path: Path to JSON annotation file
            
        Returns:
            List of annotation objects
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            # TRAE-MOD START [20250126-2000-json-parsing]: Purpose - fix JSON data structure parsing
            # Handle different JSON data structures
            if 'result' in data and 'data' in data['result']:
                return data['result']['data']
            elif 'objects' in data:
                return data['objects']
            else:
                logger.warning(f"No valid data structure found in {json_path}")
                return []
            # TRAE-MOD END [20250126-2000-json-parsing]
        except Exception as e:
            logger.error(f"Error loading JSON data from {json_path}: {e}")
            return []
    
    def _create_2d_box_from_json(self, obj_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract 2D box parameters from JSON object data.
        
        Args:
            obj_data: Object annotation data from JSON
            
        Returns:
            Dictionary with 2D box parameters or None if invalid
        """
        try:
            # TRAE-MOD START [20250126-2000-field-mapping]: Purpose - fix field name mapping for actual JSON structure
            # Extract 3D center, size, and rotation from actual JSON structure
            center_3d = obj_data.get('3Dcenter', {})
            size_3d = obj_data.get('3Dsize', {})
            
            x = center_3d.get('x', 0.0)
            y = center_3d.get('y', 0.0)
            width = size_3d.get('width', 0.0)
            length = size_3d.get('length', 0.0)
            yaw = size_3d.get('rz', 0.0)  # rotation around Z axis
            
            # Calculate occlusion level from visibility data
            visibility = obj_data.get('visibility', {})
            occlusion_rate_avg = visibility.get('occlusion_rate_avg', 0.0)
            # Map occlusion rate to level (0-4)
            if occlusion_rate_avg < 0.1:
                occlusion_level = 0
            elif occlusion_rate_avg < 0.3:
                occlusion_level = 1
            elif occlusion_rate_avg < 0.5:
                occlusion_level = 2
            elif occlusion_rate_avg < 0.8:
                occlusion_level = 3
            else:
                occlusion_level = 4
            # TRAE-MOD END [20250126-2000-field-mapping]
            
            return {
                'center_x': x,
                'center_y': y,
                'width': width,
                'length': length,
                'yaw': yaw,
                'occlusion_level': occlusion_level
            }
        except Exception as e:
            logger.warning(f"Error parsing object data: {e}")
            return None
    
    def _get_box_corners_2d(self, center_x: float, center_y: float, 
                           width: float, length: float, yaw: float) -> np.ndarray:
        """
        Compute 2D corners of a rotated bounding box.
        
        Args:
            center_x: Box center X coordinate
            center_y: Box center Y coordinate
            width: Box width (Y direction)
            length: Box length (X direction)
            yaw: Box rotation angle in radians
            
        Returns:
            Array of corner coordinates [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
        """
        # Define corners relative to center
        corners = np.array([
            [-length/2, -width/2],
            [length/2, -width/2],
            [length/2, width/2],
            [-length/2, width/2]
        ])
        
        # Apply rotation
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        rotation_matrix = np.array([
            [cos_yaw, -sin_yaw],
            [sin_yaw, cos_yaw]
        ])
        
        rotated_corners = corners @ rotation_matrix.T
        
        # Translate to world position
        world_corners = rotated_corners + np.array([center_x, center_y])
        
        return world_corners
    
    def _get_occlusion_color(self, occlusion_level: int) -> Tuple[int, int, int]:
        """
        Map occlusion level to BGR color.
        
        Args:
            occlusion_level: Occlusion level (0-4)
            
        Returns:
            BGR color tuple
        """
        color_map = {
            0: (0, 255, 0),  # Green - no occlusion
            1: (0, 255, 0),      # Green - minimal occlusion
            2: (0, 255, 0),    # Green - moderate occlusion
            3: (0, 255, 255),      # Red - high occlusion
            4: (0, 0, 255)       # Red - complete occlusion
        }
        return color_map.get(occlusion_level, (128, 128, 128))
    
    def _draw_grid(self, img: np.ndarray) -> None:
        """
        Draw coordinate grid lines and labels on the image.
        
        Args:
            img: Image array to draw on
        """
        # TRAE-MOD START [20250125-1400-grid-system]: Purpose - adjust grid for new coordinate ranges
        # Grid line spacing (every 10 meters)
        grid_spacing = 10.0
        
        # Draw vertical lines (constant Y values)
        for y in np.arange(self.y_range[0], self.y_range[1] + grid_spacing, grid_spacing):
            if y == 0:  # Center line
                color = (100, 100, 100)  # Darker gray for center
                thickness = 2
            else:
                color = (50, 50, 50)  # Dark gray for grid
                thickness = 1
            
            pixel_x, _ = self._world_to_pixel(0, y)
            cv2.line(img, (pixel_x, 0), (pixel_x, self.img_height - 1), color, thickness)
        
        # Draw horizontal lines (constant X values)
        for x in np.arange(self.x_range[0], self.x_range[1] + grid_spacing, grid_spacing):
            if x == 0:  # Center line
                color = (100, 100, 100)  # Darker gray for center
                thickness = 2
            else:
                color = (50, 50, 50)  # Dark gray for grid
                thickness = 1
            
            _, pixel_y = self._world_to_pixel(x, 0)
            cv2.line(img, (0, pixel_y), (self.img_width - 1, pixel_y), color, thickness)
        # TRAE-MOD END [20250125-1400-grid-system]
    
    def _draw_ego_vehicle(self, img: np.ndarray) -> None:
        """
        Draw the ego vehicle at the center of the coordinate system.
        
        Args:
            img: Image array to draw on
        """
        # Ego vehicle dimensions (typical car size)
        ego_length = 4.5  # meters
        ego_width = 2.0   # meters
        
        # Get ego vehicle corners
        corners = self._get_box_corners_2d(0, 0, ego_width, ego_length, 0)
        
        # Convert to pixel coordinates
        pixel_corners = []
        for corner in corners:
            pixel_x, pixel_y = self._world_to_pixel(corner[0], corner[1])
            pixel_corners.append([pixel_x, pixel_y])
        
        pixel_corners = np.array(pixel_corners, dtype=np.int32)
        
        # Draw filled ego vehicle in blue
        cv2.fillPoly(img, [pixel_corners], (255, 0, 0))  # Blue
        cv2.polylines(img, [pixel_corners], True, (200, 0, 0), 2)  # Darker blue outline
    
    def _draw_object_box(self, img: np.ndarray, box_params: Dict[str, Any]) -> None:
        """
        Draw a single object bounding box with occlusion-based coloring.
        
        Args:
            img: Image array to draw on
            box_params: Box parameters dictionary
        """
        try:
            # Extract box parameters
            center_x = box_params['center_x']
            center_y = box_params['center_y']
            width = box_params['width']
            length = box_params['length']
            yaw = box_params['yaw']
            occlusion_level = box_params['occlusion_level']
            
            # Check if box center is within coordinate ranges
            if (center_x < self.x_range[0] or center_x > self.x_range[1] or
                center_y < self.y_range[0] or center_y > self.y_range[1]):
                return  # Skip boxes outside the view range
            
            # Get box corners
            corners = self._get_box_corners_2d(center_x, center_y, width, length, yaw)
            
            # Convert to pixel coordinates
            pixel_corners = []
            for corner in corners:
                pixel_x, pixel_y = self._world_to_pixel(corner[0], corner[1])
                pixel_corners.append([pixel_x, pixel_y])
            
            pixel_corners = np.array(pixel_corners, dtype=np.int32)
            
            # Get color based on occlusion level
            color = self._get_occlusion_color(occlusion_level)
            
            # Draw box outline with increased thickness for visibility
            cv2.polylines(img, [pixel_corners], True, color, 3)
            
        except Exception as e:
            logger.warning(f"Error drawing object box: {e}")
    
    # TRAE-MOD START [20250125-1430-lidar-loading]: Purpose - implement LiDAR point cloud loading with Open3D
    def _load_point_cloud(self, pcd_path: str) -> Optional[np.ndarray]:
        """
        Load point cloud data from PCD file using Open3D.
        
        Args:
            pcd_path: Path to PCD file
            
        Returns:
            Array of XYZ coordinates or None if loading fails
        """
        try:
            if not os.path.exists(pcd_path):
                logger.warning(f"PCD file not found: {pcd_path}")
                return None
            
            # Load point cloud using Open3D
            pcd = o3d.io.read_point_cloud(pcd_path)
            
            if len(pcd.points) == 0:
                logger.warning(f"Empty point cloud: {pcd_path}")
                return None
            
            # Convert to numpy array
            points = np.asarray(pcd.points)
            
            # Filter points within coordinate ranges
            mask = ((points[:, 0] >= self.x_range[0]) & (points[:, 0] <= self.x_range[1]) &
                   (points[:, 1] >= self.y_range[0]) & (points[:, 1] <= self.y_range[1]))
            
            filtered_points = points[mask]
            
            # Subsample if too many points for performance
            if len(filtered_points) > self.max_points_render:
                indices = np.random.choice(len(filtered_points), self.max_points_render, replace=False)
                filtered_points = filtered_points[indices]
                logger.info(f"Subsampled {len(points)} points to {len(filtered_points)} for rendering")
            
            logger.info(f"Loaded {len(filtered_points)} points from {pcd_path}")
            return filtered_points
            
        except Exception as e:
            logger.error(f"Error loading point cloud from {pcd_path}: {e}")
            return None
    # TRAE-MOD END [20250125-1430-lidar-loading]
    
    # TRAE-MOD START [20250125-1430-lidar-rendering]: Purpose - implement white point cloud rendering on black background
    def _draw_point_cloud(self, img: np.ndarray, points: np.ndarray) -> None:
        """
        Render LiDAR point cloud as white points on the image.
        
        Args:
            img: Image array to draw on
            points: Array of XYZ coordinates
        """
        if points is None or len(points) == 0:
            return
        
        try:
            # Convert world coordinates to pixel coordinates (vectorized)
            pixel_coords = []
            for point in points:
                pixel_x, pixel_y = self._world_to_pixel(point[0], point[1])
                pixel_coords.append((pixel_x, pixel_y))
            
            # Draw points as white circles
            for pixel_x, pixel_y in pixel_coords:
                cv2.circle(img, (pixel_x, pixel_y), self.point_size, (255, 255, 255), -1)
            
            logger.debug(f"Rendered {len(pixel_coords)} LiDAR points")
            
        except Exception as e:
            logger.error(f"Error rendering point cloud: {e}")
    # TRAE-MOD END [20250125-1430-lidar-rendering]
    
    def _render_bev_frame(self, json_data: List[Dict[str, Any]], pcd_path: Optional[str] = None) -> np.ndarray:
        """
        Render a complete BEV frame with point cloud and detection boxes.
        
        Args:
            json_data: List of annotation data objects
            pcd_path: Optional path to PCD file
            
        Returns:
            Rendered BEV image as numpy array
        """
        # TRAE-MOD START [20250125-1410-render-pipeline]: Purpose - main BEV rendering pipeline
        # Create black canvas
        img = np.zeros((self.img_height, self.img_width, 3), dtype=np.uint8)
        
        # Step 1: Load and draw point cloud (if available)
        if pcd_path:
            points = self._load_point_cloud(pcd_path)
            if points is not None:
                self._draw_point_cloud(img, points)
        
        # Step 2: Draw grid lines
        self._draw_grid(img)
        
        # Step 3: Draw ego vehicle
        self._draw_ego_vehicle(img)
        
        # Step 4: Draw detection boxes
        # TRAE-MOD START [20250126-2100-box-rendering]: Purpose - fix detection box rendering logic
        if json_data and isinstance(json_data, list):
            logger.info(f"Processing {len(json_data)} detection objects")
            for annotation in json_data:
                box_params = self._create_2d_box_from_json(annotation)
                if box_params:
                    self._draw_object_box(img, box_params)
                    logger.debug(f"Drew box at ({box_params['center_x']:.1f}, {box_params['center_y']:.1f}) with occlusion level {box_params['occlusion_level']}")
        else:
            logger.warning("No valid annotation data found for detection box rendering")
        # TRAE-MOD END [20250126-2100-box-rendering]
        
        logger.info(f"Rendered BEV frame with dimensions {img.shape}")
        return img
        # TRAE-MOD END [20250125-1410-render-pipeline]
    
    def process_single_frame(self, json_path: str, pcd_path: Optional[str] = None, 
                           output_path: Optional[str] = None) -> Optional[np.ndarray]:
        """
        Process a single frame with JSON annotations and optional PCD data.
        
        Args:
            json_path: Path to JSON annotation file
            pcd_path: Optional path to PCD file
            output_path: Optional path to save output image
            
        Returns:
            Rendered BEV image or None if processing fails
        """
        try:
            # Load JSON data
            json_data = self._load_json_data(json_path)
            if not json_data:
                return None
            
            # Render BEV frame
            img = self._render_bev_frame(json_data, pcd_path)
            
            # Save output if path provided
            if output_path:
                cv2.imwrite(output_path, img)
                logger.info(f"Saved BEV visualization to {output_path}")
            
            return img
            
        except Exception as e:
            logger.error(f"Error processing frame {json_path}: {e}")
            return None
    
    def process_batch(self, json_dir: str, pcd_dir: Optional[str] = None, 
                     output_dir: Optional[str] = None) -> int:
        """
        Process multiple frames in batch mode.
        
        Args:
            json_dir: Directory containing JSON annotation files
            pcd_dir: Optional directory containing PCD files
            output_dir: Optional directory to save output images
            
        Returns:
            Number of successfully processed frames
        """
        try:
            if not os.path.exists(json_dir):
                logger.error(f"JSON directory not found: {json_dir}")
                return 0
            
            # Create output directory if specified
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # Find all JSON files
            json_files = [f for f in os.listdir(json_dir) if f.endswith('.json')]
            processed_count = 0
            
            logger.info(f"Processing {len(json_files)} JSON files from {json_dir}")
            
            for json_file in json_files:
                json_path = os.path.join(json_dir, json_file)
                
                # Find corresponding PCD file
                pcd_path = None
                if pcd_dir:
                    base_name = os.path.splitext(json_file)[0]
                    pcd_file = f"{base_name}.pcd"
                    pcd_path = os.path.join(pcd_dir, pcd_file)
                    if not os.path.exists(pcd_path):
                        logger.warning(f"PCD file not found for {json_file}: {pcd_path}")
                        pcd_path = None
                
                # Set output path
                output_path = None
                if output_dir:
                    base_name = os.path.splitext(json_file)[0]
                    output_path = os.path.join(output_dir, f"{base_name}_lidar_bev.png")
                
                # Process frame
                result = self.process_single_frame(json_path, pcd_path, output_path)
                if result is not None:
                    processed_count += 1
            
            logger.info(f"Successfully processed {processed_count}/{len(json_files)} frames")
            return processed_count
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            return 0


def main():
    """
    Main function for testing the LiDAR BEV visualization.
    """
    # TRAE-MOD START [20250125-1415-main-function]: Purpose - test function for LiDAR BEV visualization
    import argparse
    
    parser = argparse.ArgumentParser(description='LiDAR-enhanced BEV Visualization')
    parser.add_argument('--json_path', type=str, help='Path to JSON annotation file')
    parser.add_argument('--json_dir', type=str, help='Directory containing JSON files')
    parser.add_argument('--pcd_path', type=str, help='Path to PCD file')
    parser.add_argument('--pcd_dir', type=str, help='Directory containing PCD files')
    parser.add_argument('--output_path', type=str, help='Output image path')
    parser.add_argument('--output_dir', type=str, help='Output directory for batch processing')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = LiDARBEVVisualizationProcessor()
    
    if args.json_path:
        # Single frame processing
        result = processor.process_single_frame(
            json_path=args.json_path,
            pcd_path=args.pcd_path,
            output_path=args.output_path
        )
        if result is not None:
            print(f"Successfully processed single frame: {args.json_path}")
        else:
            print(f"Failed to process frame: {args.json_path}")
    
    elif args.json_dir:
        # Batch processing
        count = processor.process_batch(
            json_dir=args.json_dir,
            pcd_dir=args.pcd_dir,
            output_dir=args.output_dir
        )
        print(f"Processed {count} frames in batch mode")
    
    else:
        print("Please provide either --json_path or --json_dir")
        parser.print_help()
    # TRAE-MOD END [20250125-1415-main-function]


if __name__ == "__main__":
    main()