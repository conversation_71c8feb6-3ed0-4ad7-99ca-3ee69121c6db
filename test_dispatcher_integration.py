#!/usr/bin/env python3
"""
Integration test for dispatcher with conservative validation.
"""

import sys
import numpy as np
import yaml
from unittest.mock import Mock

# Add project root to path
sys.path.append('.')

from robobus_vis.visibility.dispatcher import compute_visibility


def create_mock_camera():
    """Create a mock camera for testing."""
    cam = Mock()
    cam.T_base_cam = np.eye(4)  # Identity transformation
    cam.project = Mock(return_value=np.array([[100, 100, 5.0], [120, 120, 5.0]]))
    return cam


def test_dispatcher_conservative_disabled():
    """Test dispatcher with conservative validation disabled."""
    print("Testing dispatcher with conservative validation disabled...")
    
    # Load configuration
    with open('configs/default.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Ensure conservative validation is disabled
    config['conservative_validation']['enabled'] = False
    
    # Create test data
    box3d = {'x': 10.0, 'y': 5.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_mock_camera()
    depth_img = np.full((480, 640), 10.0, dtype=np.float32)  # Uniform depth
    
    # Call dispatcher
    vis, stats = compute_visibility(
        box3d=box3d,
        cam=cam,
        depth_img=depth_img,
        cfg=config,
        method_arg='sphere',
        samples=1000,
        enable_diagnostics=True,
        debug_dir=None,
        obj_id='test_obj',
        cam_name='test_cam'
    )
    
    # Verify results
    assert isinstance(vis, float)
    assert 0.0 <= vis <= 1.0
    assert stats['conservative_enabled'] == False
    assert stats['conservative_vis'] is None
    assert stats['fusion_method'] == 'sphere_only'
    assert 'sphere_vis' in stats
    
    print(f"✓ Visibility: {vis:.3f}")
    print(f"✓ Conservative enabled: {stats['conservative_enabled']}")
    print(f"✓ Fusion method: {stats['fusion_method']}")


def test_dispatcher_conservative_enabled():
    """Test dispatcher with conservative validation enabled."""
    print("\nTesting dispatcher with conservative validation enabled...")
    
    # Load configuration
    with open('configs/default.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Enable conservative validation
    config['conservative_validation']['enabled'] = True
    
    # Create test data
    box3d = {'x': 10.0, 'y': 5.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_mock_camera()
    depth_img = np.full((480, 640), 10.0, dtype=np.float32)  # Uniform depth
    
    # Call dispatcher
    vis, stats = compute_visibility(
        box3d=box3d,
        cam=cam,
        depth_img=depth_img,
        cfg=config,
        method_arg='sphere',
        samples=1000,
        enable_diagnostics=True,
        debug_dir=None,
        obj_id='test_obj',
        cam_name='test_cam'
    )
    
    # Verify results
    assert isinstance(vis, float)
    assert 0.0 <= vis <= 1.0
    assert stats['conservative_enabled'] == True
    assert 'conservative_vis' in stats
    assert 'fusion_method' in stats
    assert 'sphere_vis' in stats
    assert 'in_loose_fov' in stats
    
    print(f"✓ Visibility: {vis:.3f}")
    print(f"✓ Conservative enabled: {stats['conservative_enabled']}")
    print(f"✓ Sphere vis: {stats['sphere_vis']:.3f}")
    print(f"✓ Conservative vis: {stats.get('conservative_vis', 'N/A')}")
    print(f"✓ Fusion method: {stats['fusion_method']}")
    print(f"✓ In loose FOV: {stats['in_loose_fov']}")


def test_dispatcher_backward_compatibility():
    """Test that dispatcher maintains backward compatibility."""
    print("\nTesting backward compatibility...")
    
    # Load configuration without conservative validation section
    config = {
        'visibility': {
            'tau_base_m': 2.0,
            'tau_scale_per_m': 0.1,
            'sphere_radius_m': 0.15,
            'occlusion_fraction_thr': 0.2,
            'min_neighbors': 3,
            'treat_no_depth_as_visible': True,
            'max_window_px': 15
        }
    }
    
    # Create test data
    box3d = {'x': 10.0, 'y': 5.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_mock_camera()
    depth_img = np.full((480, 640), 10.0, dtype=np.float32)
    
    # Call dispatcher
    vis, stats = compute_visibility(
        box3d=box3d,
        cam=cam,
        depth_img=depth_img,
        cfg=config,
        method_arg='sphere',
        samples=1000,
        enable_diagnostics=True,
        debug_dir=None,
        obj_id='test_obj',
        cam_name='test_cam'
    )
    
    # Verify results
    assert isinstance(vis, float)
    assert 0.0 <= vis <= 1.0
    assert stats['conservative_enabled'] == False  # Should default to False
    assert stats['conservative_vis'] is None
    assert stats['fusion_method'] == 'sphere_only'
    
    print(f"✓ Backward compatibility maintained")
    print(f"✓ Visibility: {vis:.3f}")
    print(f"✓ Conservative enabled: {stats['conservative_enabled']}")


if __name__ == '__main__':
    test_dispatcher_conservative_disabled()
    test_dispatcher_conservative_enabled()
    test_dispatcher_backward_compatibility()
    print("\n✅ All dispatcher integration tests passed!")