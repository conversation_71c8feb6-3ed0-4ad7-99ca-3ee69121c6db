# Camera Ego Sector Correction Plan for VisionAware Annotations

## Problem Analysis

### Issue Description

The RoboBUS dataset has directory names that don't intuitively match the actual physical camera positions, causing incorrect ego sector assignments in visibility calculations:

**Current Directory Names vs Actual Physical Positions:**

- `120_left` directory → Actually contains **rear-left** camera images
- `left_back` directory → Actually contains **front-left** camera images  
- `120_right` directory → Actually contains **rear-right** camera images
- `right_back` directory → Actually contains **front-right** camera images

**Important Note:** The camera calibration parameters in `camera_map` are **CORRECT**:

```yaml
camera_map:
  120_left: H120L-F05160609    # Correct calibration mapping
  left_back: H120L-P05160612   # Correct calibration mapping
  # etc...
```

The 3D projections work correctly. The issue is with **ego sector orientation assumptions** used for visibility filtering.

### Impact on Visibility Calculations

1. **Ego Sector Gating Errors**: Objects are filtered using wrong angular sectors in `ego_sector_map_deg`
2. **FOV Filtering Logic**: Visibility calculations assume incorrect camera orientations
3. **Multi-Camera Coverage**: Objects may be incorrectly excluded from cameras that should see them
4. **Visualization Filtering**: Batch visualization may show objects in wrong camera views

## Solution Architecture

### 1. Physical Position Mapping System

Create a two-layer mapping system:

- **Directory Name → Physical Position**: Maps dataset directory names to actual camera positions
- **Physical Position → Calibration**: Maps physical positions to calibration sensor names

### 2. Configuration-Driven Correction

Extend `configs/default.yaml` with physical position mappings that can be applied to any dataset with this naming issue.

### 3. Backward Compatibility

Maintain existing functionality while adding the correction layer as an optional feature.

## Implementation Plan

### Phase 1: Core Mapping Infrastructure

1. Create physical position mapping configuration
2. Update camera loading logic to use physical positions
3. Modify FOV and ego sector calculations

### Phase 2: Pipeline Integration  

1. Update visibility dispatcher to use corrected positions
2. Modify fusion logic for accurate multi-camera combination
3. Update visualization tools

### Phase 3: Validation and Testing

1. Test with existing datasets
2. Validate visibility calculation improvements
3. Ensure backward compatibility

## Technical Implementation

### Configuration Schema Extension

**Key Insight:** We need to correct the ego sector orientations while keeping the existing calibration mappings intact.

```yaml
# Ego sector correction for RoboBUS dataset camera positioning
ego_sector_correction:
  enabled: true  # Enable/disable ego sector correction
  
  # Corrected ego sectors based on actual camera physical positions
  # (keeping directory names but fixing the angular sectors)
  corrected_ego_sectors_deg:
    "60_front":    {center:   0.0, half: 45.0}   # Front center (unchanged)
    "120_front":   {center:   0.0, half: 75.0}   # Front center wide (unchanged)
    "120_back":    {center: 180.0, half: 75.0}   # Rear center (unchanged)
    
    # CORRECTED: These cameras are physically in different positions than names suggest
    "120_left":    {center: 135.0, half: 75.0}   # Actually rear-left (was 90°)
    "left_back":   {center:  45.0, half: 75.0}   # Actually front-left (was 150°)
    "120_right":   {center: -135.0, half: 75.0}  # Actually rear-right (was -90°)
    "right_back":  {center: -45.0, half: 75.0}   # Actually front-right (was -150°)
  
  # Optional: Logging mapping for clarity
  physical_position_notes:
    "120_left": "rear_left_camera"      # Directory name vs actual position
    "left_back": "front_left_camera"    # Directory name vs actual position
    "120_right": "rear_right_camera"    # Directory name vs actual position
    "right_back": "front_right_camera"  # Directory name vs actual position
```

**Critical:** We do NOT change the `camera_map` section as those calibrations are correct!

### Code Changes Required

1. **Camera Loading Enhancement** (`batch_visualization.py`)
2. **FOV Filter Updates** (`robobus_vis/visibility/fov_filter.py`)
3. **Configuration Utilities** (`robobus_vis/config/`)
4. **Pipeline Integration** (`robobus_vis/pipeline/run_batch.py`)

## Expected Benefits

1. **Improved Visibility Accuracy**: Correct camera orientations will fix FOV gating errors
2. **Better Multi-Camera Fusion**: Accurate positioning enables proper visibility score combination
3. **Correct Visualization**: Objects will project to appropriate camera views
4. **Future-Proof**: Solution handles all datasets with the same naming issue

## Validation Strategy

1. **Before/After Comparison**: Compare visibility scores on same dataset
2. **Camera Coverage Analysis**: Verify objects appear in correct camera sectors
3. **Visualization Validation**: Check that projections match expected camera views
4. **Performance Testing**: Ensure no degradation in processing speed

## Risk Mitigation

1. **Gradual Rollout**: Implement as optional feature first
2. **Extensive Testing**: Validate on multiple datasets before full deployment
3. **Rollback Plan**: Maintain ability to disable correction if issues arise
4. **Documentation**: Clear documentation for configuration and troubleshooting
