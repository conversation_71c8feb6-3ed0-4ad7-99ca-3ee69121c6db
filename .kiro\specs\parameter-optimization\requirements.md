# Parameter Optimization Requirements Document

## Introduction

This document outlines the requirements for optimizing the VisionAware Annotations project parameters to address excessive warning messages and improve accuracy for pure vision perception model training. The project aims to provide accurate visibility/occlusion annotations for 3D objects in multi-camera autonomous driving datasets, with particular focus on distance-based importance weighting and multi-camera fusion strategies.

## Requirements

### Requirement 1: Parameter Warning Resolution

**User Story:** As a perception engineer, I want the visibility calculation pipeline to run without excessive parameter warnings, so that I can focus on meaningful diagnostic information and ensure optimal processing performance.

#### Acceptance Criteria

1. WHEN the pipeline processes a dataset with current tau_base_m=2.0 configuration
   THEN the system SHALL NOT generate more than 5% of frames with parameter warnings
2. WHEN tau_base_m exceeds recommended maximum for any camera
   THEN the system SHALL automatically adjust to camera-specific optimal values
3. WHEN processing visibility_demo_data/clip_dataset_1
   THEN the console output SHALL show minimal parameter warnings (< 10 total warnings)
4. AFTER parameter optimization
   THEN processing time SHALL NOT increase by more than 15% compared to baseline

### Requirement 2: Distance-Based Visibility Prioritization

**User Story:** As a vision model trainer, I want closer objects to have more accurate visibility annotations than distant objects, so that the trained model prioritizes safety-critical nearby targets while allowing reasonable misses for distant small targets.

#### Acceptance Criteria

1. WHEN an object is within 30 meters of the ego vehicle
   THEN the visibility calculation SHALL use stricter occlusion thresholds (tau_base_m ≤ 0.3)
2. WHEN an object is between 30-60 meters from ego vehicle
   THEN the visibility calculation SHALL use moderate occlusion thresholds (tau_base_m ≤ 0.6)
3. WHEN an object is beyond 60 meters from ego vehicle
   THEN the visibility calculation SHALL use relaxed occlusion thresholds (tau_base_m ≤ 1.0)
4. WHEN calculating pixel area importance
   THEN objects with pixel area > 1000px² SHALL receive priority weighting in visibility scores

### Requirement 3: Multi-Camera Conservative Fusion

**User Story:** As a dataset curator, I want objects that are clearly visible in any single camera to be marked as visible overall, so that pure vision models can learn from all available visual information and reduce false negatives.

#### Acceptance Criteria

1. WHEN an object has visibility > 0.7 in any single camera view
   THEN the fused visibility score SHALL be at least 0.7 regardless of other camera scores
2. WHEN an object appears in multiple overlapping camera views
   THEN the fusion algorithm SHALL take the maximum visibility score across cameras
3. WHEN environmental occlusions affect some but not all camera views
   THEN the system SHALL preserve visibility information from unoccluded cameras
4. AFTER multi-camera fusion
   THEN the visible_views count SHALL accurately reflect cameras where object is actually visible

### Requirement 4: Camera-Specific Parameter Optimization

**User Story:** As a system administrator, I want each camera to use optimal parameters based on its physical characteristics and mounting position, so that visibility calculations are accurate across all camera views.

#### Acceptance Criteria

1. WHEN processing front-facing cameras (60_front, 120_front)
   THEN the system SHALL use tau_base_m ≤ 0.4 for high-resolution close-range accuracy
2. WHEN processing side cameras (120_left, 120_right, left_back, right_back)
   THEN the system SHALL use tau_base_m ≤ 0.6 for medium-range coverage
3. WHEN processing rear camera (120_back)
   THEN the system SHALL use tau_base_m ≤ 0.5 for parking and reversing scenarios
4. WHEN ego sector correction is enabled
   THEN parameter optimization SHALL account for corrected camera orientations

### Requirement 5: Adaptive Sphere Radius Configuration

**User Story:** As an algorithm engineer, I want the sphere projection radius to adapt based on object distance and camera resolution, so that occlusion detection is neither too sensitive (false positives) nor too coarse (missed occlusions).

#### Acceptance Criteria

1. WHEN an object is within 20 meters of ego vehicle
   THEN sphere_radius_m SHALL be ≤ 0.10 for fine-grained occlusion detection
2. WHEN an object is between 20-50 meters from ego vehicle
   THEN sphere_radius_m SHALL be between 0.10-0.20 for balanced accuracy
3. WHEN an object is beyond 50 meters from ego vehicle
   THEN sphere_radius_m SHALL be ≥ 0.20 for robust detection of distant objects
4. WHEN camera resolution is higher than 1920x1080
   THEN sphere_radius_m SHALL be reduced by 20% to leverage additional detail

### Requirement 6: Occlusion Threshold Optimization

**User Story:** As a perception researcher, I want occlusion fraction thresholds to be tuned for realistic visibility classification, so that the annotations match human perception of object visibility in camera images.

#### Acceptance Criteria

1. WHEN occlusion_fraction_thr is set for close objects (< 30m)
   THEN the threshold SHALL be ≤ 0.15 to catch partial occlusions
2. WHEN occlusion_fraction_thr is set for medium distance objects (30-60m)
   THEN the threshold SHALL be between 0.15-0.25 for balanced classification
3. WHEN occlusion_fraction_thr is set for distant objects (> 60m)
   THEN the threshold SHALL be ≥ 0.25 to avoid over-sensitivity to noise
4. WHEN validating against human annotations
   THEN the optimized thresholds SHALL achieve > 85% agreement with human visibility judgments

### Requirement 7: Performance and Diagnostic Enhancement

**User Story:** As a system operator, I want clear diagnostic information about parameter choices and processing performance, so that I can monitor system health and optimize for different datasets.

#### Acceptance Criteria

1. WHEN parameter warnings occur
   THEN the system SHALL log the specific camera, object distance, and recommended values
2. WHEN processing completes
   THEN the system SHALL output parameter usage statistics per camera
3. WHEN diagnostic mode is enabled
   THEN the system SHALL save per-object parameter decisions to diagnostic files
4. WHEN processing large datasets
   THEN memory usage SHALL remain stable and not exceed 8GB peak usage

### Requirement 8: Configuration Validation and Safety

**User Story:** As a deployment engineer, I want parameter configurations to be validated at startup with clear error messages, so that invalid configurations are caught before processing begins.

#### Acceptance Criteria

1. WHEN loading configuration files
   THEN the system SHALL validate all parameter ranges and dependencies
2. WHEN tau_base_m values exceed camera-specific maximums
   THEN the system SHALL either auto-correct or fail with clear guidance
3. WHEN sphere_radius_m values are incompatible with image resolution
   THEN the system SHALL warn and suggest appropriate values
4. WHEN conservative validation is enabled with invalid parameters
   THEN the system SHALL fall back to safe defaults with logging

### Requirement 9: Code Health and Process Efficiency Improvement

**User Story:** As a development team lead, I want to refactor the codebase to remove legacy/redundant code and streamline the data processing workflow, so that we can improve maintainability, reduce technical debt, and accelerate data processing throughput.

#### Acceptance Criteria
1.  **WHEN** a comprehensive static and dynamic code analysis is performed on the project codebase
    **THEN** a report SHALL be generated detailing unused functions, unreachable code blocks, and deprecated parameters.
2.  **WHEN** the code analysis report is reviewed
    **THEN** a phased refactoring plan SHALL be created, prioritizing the removal of high-impact redundant code without affecting core functionality.
3.  **WHEN** the refactoring plan is executed
    **THEN** a full regression test suite SHALL pass, and code coverage metrics SHALL NOT decrease, ensuring system stability.
4.  **WHEN** the end-to-end data processing workflow is mapped
    **THEN** a detailed analysis SHALL identify current bottlenecks, manual intervention points, and opportunities for parallelization.
5.  **WHEN** the workflow analysis is complete
    **THEN** a process optimization plan SHALL be proposed, outlining specific steps to automate tasks, cache intermediate results, and reduce I/O overhead.
6.  **WHEN** the proposed workflow optimizations are implemented
    **THEN** the overall processing time for a standard benchmark dataset SHALL be reduced by at least 15%, demonstrating a measurable efficiency gain.