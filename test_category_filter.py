#!/usr/bin/env python3
"""
Test script for the category filtering functionality.

This script validates that the category filtering implementation works correctly
by testing it against sample data from the visibility_demo_data directory.
"""

import json
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from robobus_vis.pipeline.run_batch import filter_objects_by_category


def test_category_filter():
    """Test the category filtering function with sample data."""
    
    # Load sample JSON data
    sample_json_path = Path("visibility_demo_data/clip_dataset_1/result_json/1733374539.701036214.json")
    
    if not sample_json_path.exists():
        print(f"Error: Sample JSON file not found: {sample_json_path}")
        return False
    
    print(f"Loading sample data from: {sample_json_path}")
    
    # Load and parse JSON
    with open(sample_json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    objects = data.get('result', {}).get('data', [])
    print(f"Loaded {len(objects)} objects from sample data")
    
    # Display sample object structure
    if objects:
        print("\nSample object structure:")
        sample_obj = objects[0]
        print(f"  ObjectID: {sample_obj.get('ObjectID', 'N/A')}")
        print(f"  ObjectType: {sample_obj.get('ObjectType', 'N/A')}")
        print(f"  label: {sample_obj.get('label', 'N/A')}")
        print(f"  3Dcenter: {sample_obj.get('3Dcenter', 'N/A')}")
    
    # Test default filtering (autonomous driving categories)
    print("\n" + "="*60)
    print("Testing default category filtering...")
    filtered_objects, filter_stats = filter_objects_by_category(objects)
    
    print(f"Filter results:")
    print(f"  Total objects: {filter_stats['total_objects']}")
    print(f"  Filtered objects: {filter_stats['filtered_objects']}")
    print(f"  Filtered out objects: {filter_stats['filtered_out_objects']}")
    print(f"  Filter efficiency: {filter_stats['filter_efficiency']:.1%}")
    print(f"  Allowed categories: {filter_stats['allowed_categories']}")
    
    # Show category distribution in original data
    print("\nOriginal data category distribution:")
    category_counts = {}
    for obj in objects:
        label = str(obj.get('label', 'unknown'))
        category_counts[label] = category_counts.get(label, 0) + 1
    
    for category, count in sorted(category_counts.items()):
        status = "✓ KEPT" if category in filter_stats['allowed_categories'] else "✗ FILTERED"
        print(f"  {category}: {count} objects [{status}]")
    
    # Test custom filtering
    print("\n" + "="*60)
    print("Testing custom category filtering (only categories 1000, 2000)...")
    custom_categories = ['1000', '2000']
    filtered_objects_custom, filter_stats_custom = filter_objects_by_category(objects, custom_categories)
    
    print(f"Custom filter results:")
    print(f"  Total objects: {filter_stats_custom['total_objects']}")
    print(f"  Filtered objects: {filter_stats_custom['filtered_objects']}")
    print(f"  Filtered out objects: {filter_stats_custom['filtered_out_objects']}")
    print(f"  Filter efficiency: {filter_stats_custom['filter_efficiency']:.1%}")
    print(f"  Allowed categories: {filter_stats_custom['allowed_categories']}")
    
    # Validate filtered results
    print("\n" + "="*60)
    print("Validating filtered results...")
    
    # Check that all filtered objects have allowed labels
    validation_passed = True
    allowed_set = set(filter_stats['allowed_categories'])
    
    for obj in filtered_objects:
        obj_label = str(obj.get('label', ''))
        if obj_label not in allowed_set:
            print(f"ERROR: Filtered object has disallowed label: {obj_label}")
            validation_passed = False
    
    if validation_passed:
        print("✓ All filtered objects have allowed category labels")
    else:
        print("✗ Validation failed - some filtered objects have disallowed labels")
    
    # Check that no allowed objects were filtered out
    filtered_labels = set(str(obj.get('label', '')) for obj in filtered_objects)
    original_allowed_labels = set(str(obj.get('label', '')) for obj in objects if str(obj.get('label', '')) in allowed_set)
    
    if filtered_labels == original_allowed_labels:
        print("✓ No allowed objects were incorrectly filtered out")
    else:
        print("✗ Some allowed objects were incorrectly filtered out")
        print(f"  Expected labels: {original_allowed_labels}")
        print(f"  Actual labels: {filtered_labels}")
        validation_passed = False
    
    print("\n" + "="*60)
    if validation_passed:
        print("✓ Category filtering test PASSED")
        return True
    else:
        print("✗ Category filtering test FAILED")
        return False


def test_edge_cases():
    """Test edge cases for the category filtering function."""
    
    print("\n" + "="*60)
    print("Testing edge cases...")
    
    # Test empty objects list
    filtered, stats = filter_objects_by_category([])
    assert len(filtered) == 0
    assert stats['total_objects'] == 0
    assert stats['filter_efficiency'] == 0.0
    print("✓ Empty objects list handled correctly")
    
    # Test objects without label field
    objects_no_label = [{'ObjectID': 'test1', 'ObjectType': 'Car'}]
    filtered, stats = filter_objects_by_category(objects_no_label)
    assert len(filtered) == 0  # Should be filtered out due to missing/empty label
    print("✓ Objects without label field handled correctly")
    
    # Test objects with non-string labels
    objects_numeric_label = [{'ObjectID': 'test2', 'label': 1000}]
    filtered, stats = filter_objects_by_category(objects_numeric_label)
    assert len(filtered) == 1  # Should be kept (converted to string)
    print("✓ Objects with numeric labels handled correctly")
    
    # Test custom empty allowed categories
    objects_with_labels = [{'ObjectID': 'test3', 'label': '1000'}]
    filtered, stats = filter_objects_by_category(objects_with_labels, allowed_categories=[])
    assert len(filtered) == 0  # Should filter out everything
    print("✓ Empty allowed categories handled correctly")
    
    print("✓ All edge cases passed")


if __name__ == "__main__":
    print("Category Filter Test Suite")
    print("="*60)
    
    try:
        # Run main test
        main_test_passed = test_category_filter()
        
        # Run edge case tests
        test_edge_cases()
        
        if main_test_passed:
            print("\n🎉 All tests passed! Category filtering is working correctly.")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
