"""
Ego Sector Correction Module

Handles the ego sector orientation corrections for RoboBUS datasets where
directory names don't intuitively match actual physical camera positions.
The camera calibrations are correct, but ego sector angles need adjustment.
"""

import logging
from typing import Dict, Optional, Tuple, Any
import numpy as np

logger = logging.getLogger(__name__)

class EgoSectorCorrector:
    """
    Corrects ego sector orientations for datasets where directory names
    don't match actual physical camera positions.
    
    Note: Camera calibrations remain unchanged - only ego sector angles are corrected.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the ego sector corrector.
        
        Args:
            config: Configuration dictionary containing ego_sector_correction section
        """
        self.config = config
        self.correction_config = config.get('ego_sector_correction', {})
        self.enabled = self.correction_config.get('enabled', False)
        
        # Load corrected ego sectors
        self.corrected_ego_sectors = self.correction_config.get('corrected_ego_sectors_deg', {})
        self.position_notes = self.correction_config.get('physical_position_notes', {})
        
        if self.enabled:
            logger.info(f"Ego sector correction enabled for {len(self.corrected_ego_sectors)} cameras")
            self._validate_configuration()
        else:
            logger.info("Ego sector correction disabled")
    
    def _validate_configuration(self):
        """Validate that correction configuration is complete and consistent."""
        if not self.corrected_ego_sectors:
            logger.warning("Ego sector correction enabled but no corrected_ego_sectors_deg found")
            return
        
        # Check that corrected sectors have valid parameters
        for camera, sector in self.corrected_ego_sectors.items():
            if 'center' not in sector or 'half' not in sector:
                logger.warning(f"Invalid ego sector definition for {camera}: {sector}")
        
        logger.info(f"Validated ego sector corrections for {len(self.corrected_ego_sectors)} cameras")
    
    def get_corrected_ego_sector(self, directory_name: str) -> Optional[Dict[str, float]]:
        """
        Get corrected ego sector parameters for a camera directory.
        
        Args:
            directory_name: Camera directory name (e.g., "120_left")
            
        Returns:
            Dictionary with 'center' and 'half' keys, or None if not found
        """
        if not self.enabled:
            return None
        
        return self.corrected_ego_sectors.get(directory_name)
    
    def get_physical_position_note(self, directory_name: str) -> Optional[str]:
        """
        Get a human-readable note about the actual physical position.
        
        Args:
            directory_name: Camera directory name
            
        Returns:
            Physical position description or None
        """
        if not self.enabled:
            return None
        
        return self.position_notes.get(directory_name)
    
    def get_corrected_ego_sectors_map(self) -> Dict[str, Dict[str, float]]:
        """
        Get complete ego sectors map with corrections applied.
        
        Returns:
            Dictionary mapping directory names to ego sector parameters
        """
        if not self.enabled:
            return self.config.get('ego_sector_map_deg', {})
        
        corrected_map = {}
        original_map = self.config.get('ego_sector_map_deg', {})
        
        # Start with original map
        corrected_map.update(original_map)
        
        # Apply corrections where available
        for dir_name, corrected_sector in self.corrected_ego_sectors.items():
            if dir_name in original_map:  # Only correct cameras that exist in original config
                corrected_map[dir_name] = corrected_sector
                logger.debug(f"Applied ego sector correction for {dir_name}: {corrected_sector}")
        
        return corrected_map
    
    def log_correction_summary(self):
        """Log a summary of applied corrections for debugging."""
        if not self.enabled:
            logger.info("Ego sector correction is disabled")
            return
        
        logger.info("Ego Sector Correction Summary:")
        logger.info("=" * 50)
        
        original_map = self.config.get('ego_sector_map_deg', {})
        
        for dir_name, corrected_sector in self.corrected_ego_sectors.items():
            original_sector = original_map.get(dir_name, {})
            position_note = self.get_physical_position_note(dir_name)
            
            logger.info(f"Camera Directory: {dir_name}")
            if position_note:
                logger.info(f"  Actual Position: {position_note}")
            if original_sector:
                logger.info(f"  Original Ego Sector: center={original_sector.get('center', 'N/A')}°, half={original_sector.get('half', 'N/A')}°")
            logger.info(f"  Corrected Ego Sector: center={corrected_sector['center']}°, half={corrected_sector['half']}°")
            logger.info("")


def create_camera_corrector(config: Dict[str, Any]) -> EgoSectorCorrector:
    """
    Factory function to create an ego sector corrector.
    
    Args:
        config: Full configuration dictionary
        
    Returns:
        EgoSectorCorrector instance
    """
    return EgoSectorCorrector(config)