import numpy as np
import cv2
import logging
from typing import Dict, Tuple

logger = logging.getLogger(__name__)


def _pixel_radius_from_metric(cam, depth_z: float, sphere_radius_m: float, max_window_px: int) -> int:
    """Approximate projected pixel radius for a small sphere of metric radius at depth z.
    Uses camera intrinsics to map meters→pixels; clamps to [1, max_window_px].
    """
    if not np.isfinite(depth_z) or depth_z <= 0:
        return 1
    fx = cam.K[0, 0]
    fy = cam.K[1, 1]
    # project metric radius to pixels along u and v axes
    r_u = fx * sphere_radius_m / depth_z
    r_v = fy * sphere_radius_m / depth_z
    r = int(max(1, min(max_window_px, np.ceil(max(r_u, r_v)))))
    return r


def visibility_via_sphere_projection(
    box3d: Dict,
    cam,
    depth_img: np.ndarray,
    *,
    sphere_radius_m: float = 0.15,
    tau_base_m: float = 0.3,
    tau_scale_per_m: float = 0.02,
    occlusion_fraction_thr: float = 0.2,
    min_neighbors: int = 3,
    treat_no_depth_as_visible: bool = True,
    max_window_px: int = 15,
) -> Tuple[float, Dict]:
    """
    Sphere projection visibility estimation for 3D objects.

    This algorithm addresses limitations of traditional Z-buffer approaches by using
    neighborhood analysis around each projected surface point. Instead of single-pixel
    depth comparison, it analyzes the depth distribution within a circular region
    corresponding to a metric sphere in 3D space.

    Algorithm:
      1) Project each surface point to image coordinates (u,v,z)
      2) Compute circular pixel window with radius = fx * sphere_radius_m / z
      3) Within the circular mask, count scene depths closer than (z - tau)
      4) Mark point visible if occlusion_fraction < threshold
      5) Handle missing depth data based on treat_no_depth_as_visible

    Args:
        box3d: Dictionary with 'surface_pts' key containing (N x 3) surface points
        cam: Camera object with intrinsic matrix K and project() method
        depth_img: (H, W) depth map with np.inf for missing depths
        sphere_radius_m: Metric radius of analysis sphere (default: 0.15m)
        tau_base_m: Base depth tolerance in meters (default: 0.3m)
        tau_scale_per_m: Distance-dependent tolerance scaling (default: 0.02)
        occlusion_fraction_thr: Visibility threshold - visible if occluded_fraction < this
        min_neighbors: Minimum neighbors required (currently unused, reserved for future)
        treat_no_depth_as_visible: How to handle regions with no LiDAR data
        max_window_px: Maximum pixel window radius to prevent excessive computation

    Returns:
        Tuple of (visibility_ratio, statistics_dict) where:
        - visibility_ratio: Float in [0,1] representing fraction of visible surface points
        - statistics_dict: Contains 'samples', 'considered', 'visible', 'avg_pixel_radius', 'avg_z'

    Note:
        This method is particularly effective for sparse LiDAR point clouds and noisy
        sensor data, providing more robust visibility estimation than pixel-wise methods.
    """
    H, W = depth_img.shape

    pts = box3d.get('surface_pts', None)
    if pts is None or len(pts) == 0:
        return 0.0, {"samples": 0, "considered": 0, "visible": 0}

    uvz = cam.project(pts)
    u = np.round(uvz[:, 0]).astype(int)
    v = np.round(uvz[:, 1]).astype(int)
    z = uvz[:, 2]

    in_img = np.isfinite(z) & (z > 0) & (u >= 0) & (u < W) & (v >= 0) & (v < H)
    u = u[in_img]
    v = v[in_img]
    z = z[in_img]

    if len(u) == 0:
        return 0.0, {"samples": 0, "considered": 0, "visible": 0}

    total = len(u)
    considered = 0
    visible_pts = 0
    sum_r = 0.0

    for i in range(total):
        ui = u[i]
        vi = v[i]
        zi = z[i]

        # Adaptive tolerance (meters)
        tau = max(tau_base_m, tau_scale_per_m * float(zi))

        r = _pixel_radius_from_metric(cam, float(zi), sphere_radius_m, max_window_px)
        sum_r += r

        # Create circular mask for more accurate sphere projection
        u0 = int(max(0, ui - r))
        u1 = int(min(W, ui + r + 1))
        v0 = int(max(0, vi - r))
        v1 = int(min(H, vi + r + 1))

        win = depth_img[v0:v1, u0:u1]
        if win.size == 0:
            # No pixels to evaluate; skip counting as considered
            continue

        # Create circular mask within the window
        win_h, win_w = win.shape
        mask = np.zeros((win_h, win_w), dtype=np.uint8)
        center_u = ui - u0
        center_v = vi - v0
        if 0 <= center_u < win_w and 0 <= center_v < win_h:
            cv2.circle(mask, (center_u, center_v), r, 1, -1)

        # Apply circular mask to window
        circular_mask = mask.astype(bool)
        win_masked = win[circular_mask]

        if win_masked.size == 0:
            continue

        finite = np.isfinite(win_masked)
        n_finite = int(finite.sum())

        if n_finite == 0:
            considered += 1
            if treat_no_depth_as_visible:
                visible_pts += 1
            continue

        # Count neighbors that are in front of the sphere surface (occluders)
        closer = (win_masked[finite] < (zi - tau))
        frac_closer = float(closer.sum()) / float(n_finite)

        considered += 1
        if frac_closer < occlusion_fraction_thr:
            visible_pts += 1

    ratio = float(visible_pts) / float(max(1, considered))
    stats = {
        "samples": int(total),
        "considered": int(considered),
        "visible": int(visible_pts),
        "avg_pixel_radius": float(sum_r / max(1, considered)),
        "avg_z": float(np.mean(z)) if len(z) > 0 else 0.0,
    }

    logger.debug(
        f"SphereProjection: samples={total}, considered={considered}, visible={visible_pts}, "
        f"ratio={ratio:.3f}, avg_r={stats['avg_pixel_radius']:.2f}"
    )

    return ratio, stats

