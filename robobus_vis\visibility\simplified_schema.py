# TRAE-MOD START [20250121-1430-simplified-schema]: Purpose - implement simplified output schema with essential fields only
from typing import Dict, List, Optional, Any
import numpy as np
from .fusion import (
    fuse_camera_visibility,
    to_occlusion_level,
    validate_occlusion_consistency,
    calculate_bev_weight,
    calculate_reliability_weight
)


class SimplifiedOutputSchema:
    """
    Simplified output schema that focuses on essential visibility metrics only.
    
    Key optimizations:
    1. Removes redundant occlusion_rate_avg (can be derived from visibility_rate_avg)
    2. Reduces precision from 15 decimals to 3 decimals for visibility scores
    3. Makes derived fields optional and computed on-demand
    4. Provides schema variants for different use cases
    """
    
    @staticmethod
    def create_essential_visibility(per_cam: Dict[str, float]) -> Dict[str, Any]:
        """
        Create essential visibility metrics only.
        
        Returns:
            Dictionary with core visibility fields:
            - visibility_rate_avg: Average visibility across cameras (3 decimal precision)
            - visibility_rate_max: Maximum visibility across cameras (3 decimal precision)
            - occlusion_level: Discrete occlusion level (1-4)
            - visible_in_views: List of camera views where object is visible
        """
        # Calculate core metrics
        vis_avg, vis_max, visible_views, fusion_stats = fuse_camera_visibility(per_cam, include_noFOV_as_zero=False)
        occlusion_level = to_occlusion_level(vis_avg)
        
        # Validate consistency and correct if needed
        is_consistent, corrected_level, reason = validate_occlusion_consistency(
            vis_avg, visible_views, occlusion_level
        )
        
        if not is_consistent:
            occlusion_level = corrected_level
        
        return {
            "visibility_rate_avg": round(vis_avg, 3),  # Reduced precision from 15 to 3 decimals
            "visibility_rate_max": round(vis_max, 3),  # Reduced precision from 15 to 3 decimals
            "occlusion_level": occlusion_level,
            "visible_in_views": visible_views
        }
    
    @staticmethod
    def create_training_visibility(per_cam: Dict[str, float], stats_total: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Create visibility metrics optimized for training use cases.
        
        Includes additional fields useful for model training:
        - best_view: Camera with highest visibility
        - reliability_weight: Training sample weight based on data quality
        - bev_weight: Weight for BEV transformation
        """
        essential = SimplifiedOutputSchema.create_essential_visibility(per_cam)
        
        # Find best view (camera with highest visibility)
        best_view = None
        if per_cam:
            valid_cams = {cam: vis for cam, vis in per_cam.items() if vis is not None and vis > 0}
            if valid_cams:
                best_view = max(valid_cams.keys(), key=lambda cam: valid_cams[cam])
        
        # Calculate additional training metrics
        reliability_weight = calculate_reliability_weight(stats_total or {})
        bev_weight = calculate_bev_weight(per_cam)
        
        training_fields = {
            "best_view": best_view,
            "reliability_weight": round(reliability_weight, 3),
            "bev_weight": round(bev_weight, 3)
        }
        
        return {**essential, **training_fields}
    
    @staticmethod
    def create_debug_visibility(per_cam: Dict[str, float], stats_total: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Create comprehensive visibility metrics for debugging and analysis.
        
        Includes all fields from training schema plus:
        - per_camera: Individual camera visibility scores
        - validation_status: Consistency check results
        """
        training = SimplifiedOutputSchema.create_training_visibility(per_cam, stats_total)
        
        # Add per-camera details (with reduced precision)
        per_camera_clean = {}
        for cam, vis in per_cam.items():
            if vis is not None:
                per_camera_clean[cam] = round(vis, 3)
            else:
                per_camera_clean[cam] = None
        
        # Validation status
        vis_avg = training["visibility_rate_avg"]
        visible_views = training["visible_in_views"]
        occlusion_level = training["occlusion_level"]
        
        is_consistent, corrected_level, reason = validate_occlusion_consistency(
            vis_avg, visible_views, occlusion_level
        )
        
        debug_fields = {
            "per_camera": per_camera_clean,
            "validation_status": {
                "is_consistent": is_consistent,
                "reason": reason if not is_consistent else "ok"
            }
        }
        
        return {**training, **debug_fields}
    
    @staticmethod
    def create_minimal_visibility(per_cam: Dict[str, float]) -> Dict[str, Any]:
        """
        Create minimal visibility metrics for lightweight applications.
        
        Only includes:
        - occlusion_level: Discrete occlusion level (1-4)
        - visibility_rate_avg: Average visibility (3 decimal precision)
        """
        vis_avg, _, _, _ = fuse_camera_visibility(per_cam, include_noFOV_as_zero=False)
        occlusion_level = to_occlusion_level(vis_avg)
        
        return {
            "occlusion_level": occlusion_level,
            "visibility_rate_avg": round(vis_avg, 3)
        }


class SchemaVariants:
    """
    Schema variant selector based on use case.
    """
    
    ESSENTIAL = "essential"      # Core visibility metrics only
    TRAINING = "training"        # Optimized for model training
    DEBUG = "debug"              # Full metrics for debugging
    MINIMAL = "minimal"          # Lightweight for production
    
    @staticmethod
    def get_visibility_fields(variant: str, per_cam: Dict[str, float], stats_total: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Get visibility fields based on schema variant.
        
        Args:
            variant: Schema variant (essential, training, debug, minimal)
            per_cam: Per-camera visibility scores
            stats_total: Optional statistics for enhanced metrics
            
        Returns:
            Dictionary with visibility fields based on selected variant
        """
        if variant == SchemaVariants.ESSENTIAL:
            return SimplifiedOutputSchema.create_essential_visibility(per_cam)
        elif variant == SchemaVariants.TRAINING:
            return SimplifiedOutputSchema.create_training_visibility(per_cam, stats_total)
        elif variant == SchemaVariants.DEBUG:
            return SimplifiedOutputSchema.create_debug_visibility(per_cam, stats_total)
        elif variant == SchemaVariants.MINIMAL:
            return SimplifiedOutputSchema.create_minimal_visibility(per_cam)
        else:
            raise ValueError(f"Unknown schema variant: {variant}. Available: {[SchemaVariants.ESSENTIAL, SchemaVariants.TRAINING, SchemaVariants.DEBUG, SchemaVariants.MINIMAL]}")


def optimize_null_handling(visibility_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Optimize null handling by removing null fields and using compact representations.
    
    Args:
        visibility_data: Visibility data dictionary
        
    Returns:
        Optimized dictionary with null values handled efficiently
    """
    optimized = {}
    
    for key, value in visibility_data.items():
        if value is None:
            continue  # Skip null values entirely
        elif isinstance(value, dict):
            # Recursively optimize nested dictionaries
            nested_optimized = optimize_null_handling(value)
            if nested_optimized:  # Only include if not empty
                optimized[key] = nested_optimized
        elif isinstance(value, list) and len(value) == 0:
            continue  # Skip empty lists
        else:
            optimized[key] = value
    
    return optimized


def create_object_visibility_data(per_cam: Dict[str, float], 
                                 stats_total: Optional[Dict] = None,
                                 schema_variant: str = SchemaVariants.ESSENTIAL,
                                 optimize_nulls: bool = True) -> Dict[str, Any]:
    """
    Main function to create visibility data for an object using simplified schema.
    
    Args:
        per_cam: Per-camera visibility scores
        stats_total: Optional statistics for enhanced metrics
        schema_variant: Schema variant to use (essential, training, debug, minimal)
        optimize_nulls: Whether to optimize null value handling
        
    Returns:
        Dictionary with visibility data according to selected schema variant
    """
    # Get visibility fields based on variant
    visibility_data = SchemaVariants.get_visibility_fields(schema_variant, per_cam, stats_total)
    
    # Optimize null handling if requested
    if optimize_nulls:
        visibility_data = optimize_null_handling(visibility_data)
    
    return visibility_data
# TRAE-MOD END [20250121-1430-simplified-schema]