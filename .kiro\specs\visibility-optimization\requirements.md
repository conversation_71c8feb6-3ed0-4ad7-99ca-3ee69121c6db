# Requirements Document

## Introduction


This document outlines the requirements for enhancing the visibility calculation in the VisionAware Annotations project. The goal is to improve accuracy in determining 3D object visibility while minimizing false negatives—visible objects incorrectly marked as occluded—especially in the presence of environmental occlusions and calibration inaccuracies.

The Neighborhood-Z Algorithm currently deployed in production is not the same algorithm described in the referenced research paper（Sphere Projection Visibility Algorithm）, despite sharing a similar name.
• Neighborhood-Z Algorithm (current): operates in 2-D image space; for each projected point it builds a circular neighborhood, analyzes the local depth distribution, and applies an adaptive tolerance τ to decide occlusion.
• Research Algorithm (paper：3D Object Visibility Prediction in Autonomous Driving): operates in 3-D space; projects 3-D bounding boxes onto a unit sphere centered at the ego-vehicle, computes overlapping solid angles, and derives visibility without relying on point-cloud density.
The Neighborhood-Z Algorithm handles environmental occlusions robustly, but its accuracy is sensitive to sparse or noisy point clouds and to camera-calibration drift. The Research Algorithm does not handle unlabeled environmental occlusions at all; it only evaluates inter-object visibility among labeled 3-D boxes, yet it is completely independent of point-cloud density.
## Requirements

### Requirement 1

**User Story:** As a perception engineer, I want reliable visibility scores that account for environmental occlusions without relying on precise calibration, so that training data for vision-only models has fewer false positives.

#### Acceptance Criteria

1. WHEN an object is partially occluded by an unlabeled environmental element (e.g., a barrier) THEN the system SHALL still assign a visibility score above 0.5 if the object is partially visible in any camera
2. WHEN depth data is sparse or missing in a camera view THEN the system SHALL assume visibility for that camera unless overwhelming evidence of occlusion exists
3. WHEN processing validation datasets THEN the visibility scores SHALL have a false negative rate of less than 5%
4. WHEN conservative validation is enabled THEN the system SHALL use generous azimuth tolerances to include edge cases

### Requirement 2

**User Story:** As a data curator, I want consistent visibility annotations across multi-camera views, even with calibration errors, so that downstream training pipelines can filter data confidently.

#### Acceptance Criteria

1. WHEN camera calibration is inaccurate THEN the system SHALL use sphere projection as a fallback to compute visibility without relying on camera parameters
2. WHEN an object is in the overlapping region of multiple cameras THEN the system SHALL fuse visibility scores by taking the maximum value across cameras to avoid underestimation
3. WHEN generating output THEN the system SHALL provide per-camera visibility scores and a global visibility score in the JSON format
4. WHEN multiple cameras view the same object THEN the system SHALL maintain backward compatibility with existing JSON output formats

### Requirement 3

**User Story:** As a system administrator, I want configurable conservative validation parameters, so that I can tune the system for different datasets and use cases.

#### Acceptance Criteria

1. WHEN configuring the system THEN the system SHALL provide adjustable azimuth tolerance parameters for loose FOV gating
2. WHEN depth data is insufficient THEN the system SHALL allow configuration of minimum depth point thresholds
3. WHEN processing objects THEN the system SHALL support configurable tau multipliers for depth validation tolerances
4. WHEN conservative validation is disabled THEN the system SHALL fall back to the original sphere projection algorithm

### Requirement 4

**User Story:** As a performance engineer, I want the enhanced visibility system to maintain existing performance characteristics, so that batch processing remains efficient.

#### Acceptance Criteria

1. WHEN processing large datasets THEN the system SHALL maintain parallel processing capabilities using ThreadPoolExecutor
2. WHEN generating depth maps THEN the system SHALL continue to use per-frame, per-camera caching for efficiency
3. WHEN conservative validation is enabled THEN the system SHALL not degrade performance by more than 20% compared to baseline
4. WHEN processing objects THEN the system SHALL maintain instrumented logging for performance monitoring

### Requirement 5

**User Story:** As a developer, I want modular conservative validation components, so that I can test and maintain the system effectively.

#### Acceptance Criteria

1. WHEN implementing conservative validation THEN the system SHALL provide separate functions for loose FOV gating and depth map validation
2. WHEN integrating with existing code THEN the system SHALL maintain clear separation between sphere projection and conservative validation logic
3. WHEN testing the system THEN the system SHALL provide unit tests for edge cases in conservative validation
4. WHEN debugging issues THEN the system SHALL provide clear diagnostic outputs for validation decisions