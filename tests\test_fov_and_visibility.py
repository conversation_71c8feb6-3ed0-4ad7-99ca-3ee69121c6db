import pytest
import numpy as np
import math
from pathlib import Path
import sys

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from robobus_vis.calib.camera_model import Camera
from robobus_vis.visibility.fov_filter import ego_sector_gate, cam_fov_gate_point, cam_fov_gate_corners
from robobus_vis.visibility.dispatcher import compute_visibility
from robobus_vis.geometry.box3d import sample_box_surface, box_corners
from robobus_vis.geometry.depth_tools import pointcloud_to_depth


def create_test_camera(name: str, fov_h_deg: float = 120.0, fov_v_deg: float = 50.0,
                      position: tuple = (0, 0, 0), yaw_deg: float = 0.0):
    """Create a test camera with specified FOV and position."""
    # Simple pinhole camera model
    K = np.array([[800.0, 0, 640.0],
                  [0, 800.0, 360.0],
                  [0, 0, 1.0]], dtype=float)

    # Create transformation matrix (cam <- base)
    # For camera coordinate system: +X right, +Y down, +Z forward
    yaw_rad = math.radians(yaw_deg)
    c, s = np.cos(yaw_rad), np.sin(yaw_rad)

    # Camera rotation in base frame
    R_base_cam = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]], dtype=float)
    t_base_cam = np.array(position, dtype=float)

    # T_base_cam transforms base points to camera frame
    T_base_cam = np.eye(4)
    T_base_cam[:3, :3] = R_base_cam.T  # R_cam_base = R_base_cam.T
    T_base_cam[:3, 3] = -R_base_cam.T @ t_base_cam
    
    return Camera(
        name=name,
        K=K,
        dist=None,
        width=1280,
        height=720,
        T_base_cam=T_base_cam
    )


def generate_test_vehicle(bearing: float, distance: float = 20.0, size: tuple = (4.0, 2.0, 1.5)):
    """Generate a test vehicle at specified bearing and distance."""
    bearing_rad = math.radians(bearing)
    x = distance * math.cos(bearing_rad)
    y = distance * math.sin(bearing_rad)
    z = 0.0  # Ground level
    
    l, w, h = size
    alpha = 0.0  # No rotation for simplicity
    
    # Generate surface points
    surface_pts = sample_box_surface((x, y, z), l, w, h, alpha, samples_per_face=100)
    corners = box_corners((x, y, z), l, w, h, alpha)
    
    return {
        'center': (x, y, z),
        'size': size,
        'alpha': alpha,
        'surface_pts': surface_pts,
        'corners': corners
    }


def generate_occluded_vehicle(occlusion_percentage: float = 0.4):
    """Generate a vehicle with a known occluder creating specified occlusion."""
    # Vehicle at origin for simplicity
    vehicle = generate_test_vehicle(bearing=0, distance=15.0)
    
    # Create occluder in front of vehicle
    occluder_distance = 10.0
    occluder = generate_test_vehicle(bearing=0, distance=occluder_distance, size=(2.0, 2.0, 2.0))
    
    return vehicle, occluder


def create_synthetic_depth_map(camera: Camera, objects: list, width: int = 1280, height: int = 720):
    """Create a synthetic depth map from objects."""
    depth_map = np.full((height, width), np.inf, dtype=np.float32)
    
    for obj in objects:
        surface_pts = obj['surface_pts']
        if len(surface_pts) == 0:
            continue
            
        uvz = camera.project(surface_pts)
        u = np.round(uvz[:, 0]).astype(int)
        v = np.round(uvz[:, 1]).astype(int)
        z = uvz[:, 2]
        
        # Filter valid projections
        valid = (z > 0) & (u >= 0) & (u < width) & (v >= 0) & (v < height)
        u, v, z = u[valid], v[valid], z[valid]
        
        # Update depth map with minimum depth per pixel
        for i in range(len(u)):
            if z[i] < depth_map[v[i], u[i]]:
                depth_map[v[i], u[i]] = z[i]
    
    return depth_map


def test_left_back_camera_edge():
    """Validate camera-specific parameter overrides and FOV fallback functionality."""
    # Create test configuration matching documentation requirements
    test_config = {
        'fov_filter': {'soft_margin_deg': 2.0, 'fallback_on_filter': True},
        'visibility_overrides': {'left_back': {'tau_base_m': 0.5, 'tau_scale_per_m': 0.05}},
        'visibility': {'tau_base_m': 0.3, 'tau_scale_per_m': 0.02, 'sphere_radius_m': 0.15}
    }

    # Test that camera-specific overrides are applied correctly
    # Use identity transformation for simplicity
    simple_cam = Camera(
        name='left_back',
        K=np.array([[800.0, 0, 640.0], [0, 800.0, 360.0], [0, 0, 1.0]], dtype=float),
        dist=None,
        width=1280,
        height=720,
        T_base_cam=np.eye(4)  # Identity - camera at origin looking down +Z
    )

    # Create object in front of camera (positive Z)
    vehicle = {
        'center': (0.0, 0.0, 10.0),  # 10m in front
        'surface_pts': np.array([[0, 0, 10], [1, 0, 10], [0, 1, 10], [-1, 0, 10], [0, -1, 10]], dtype=float)
    }

    # Create simple depth map
    depth_map = np.full((720, 1280), np.inf, dtype=np.float32)
    depth_map[350:370, 630:650] = 10.0  # Object depth at center

    box3d = {'surface_pts': vehicle['surface_pts']}

    # Test visibility calculation with overrides
    visibility, stats = compute_visibility(
        box3d, simple_cam, depth_map, test_config, 'sphere', 0,
        False, None, 'test_obj', 'left_back'
    )

    # Verify that computation ran (even if visibility is low due to simple setup)
    assert stats['samples'] > 0, "Should have processed some surface samples"
    assert stats['considered'] >= 0, "Should have considered some samples"

    # Test that overrides are being applied by checking for the warning
    # (tau_base_m=0.5 should not trigger warning, but if we set it higher it would)
    test_config_high_tau = {
        'visibility_overrides': {'left_back': {'tau_base_m': 0.7}},  # Should trigger warning
        'visibility': {'tau_base_m': 0.3}
    }

    # This should trigger the parameter validation warning (now logged instead of using warnings module)
    import logging
    import io
    
    # Capture log output
    log_capture_string = io.StringIO()
    ch = logging.StreamHandler(log_capture_string)
    ch.setLevel(logging.WARNING)
    
    # Get the dispatcher logger
    dispatcher_logger = logging.getLogger('robobus_vis.visibility.dispatcher')
    dispatcher_logger.addHandler(ch)
    dispatcher_logger.setLevel(logging.WARNING)
    
    try:
        visibility2, stats2 = compute_visibility(
            box3d, simple_cam, depth_map, test_config_high_tau, 'sphere', 0,
            False, None, 'test_obj', 'left_back'
        )
        
        # Check that warning was logged for high tau_base_m
        log_contents = log_capture_string.getvalue()
        warning_found = "tau_base_m=0.7 exceeds recommended maximum" in log_contents
        assert warning_found, f"Should have warned about high tau_base_m parameter. Log contents: {log_contents}"
    finally:
        dispatcher_logger.removeHandler(ch)


def test_occlusion_stress():
    """Validate visibility calculation under occlusion per vision-aware-annotations.md"""
    # Create test configuration
    test_config = {
        'visibility': {
            'tau_base_m': 0.4, 'tau_scale_per_m': 0.04, 'sphere_radius_m': 0.15,
            'occlusion_fraction_thr': 0.2, 'treat_no_depth_as_visible': True
        }
    }

    # Simple camera setup
    simple_cam = Camera(
        name='front',
        K=np.array([[800.0, 0, 640.0], [0, 800.0, 360.0], [0, 0, 1.0]], dtype=float),
        dist=None,
        width=1280,
        height=720,
        T_base_cam=np.eye(4)
    )

    # Create vehicle surface points at distance 15m
    vehicle_surface = np.array([
        [0, 0, 15], [1, 0, 15], [-1, 0, 15], [0, 1, 15], [0, -1, 15],  # Center points
        [2, 0, 15], [-2, 0, 15], [0, 2, 15], [0, -2, 15]  # Edge points
    ], dtype=float)

    # Create depth map with partial occlusion
    depth_map = np.full((720, 1280), np.inf, dtype=np.float32)

    # Vehicle depth at 15m (unoccluded regions)
    depth_map[350:370, 620:660] = 15.0  # Center region
    depth_map[350:370, 680:720] = 15.0  # Right region

    # Occluder depth at 10m (blocks some vehicle regions)
    depth_map[350:370, 640:680] = 10.0  # Occluder in middle-left

    box3d = {'surface_pts': vehicle_surface}

    # Calculate visibility
    visibility, stats = compute_visibility(
        box3d, simple_cam, depth_map, test_config, 'sphere', 0,
        False, None, 'test_obj', 'front'
    )

    # Verify that computation ran and found some visibility
    assert stats['samples'] > 0, "Should have processed surface samples"
    assert stats['considered'] > 0, "Should have considered some samples"

    # With partial occlusion, expect some visibility (not zero, not full)
    assert 0.0 <= visibility <= 1.0, f"Visibility should be in [0,1], got {visibility}"

    # Test that algorithm responds to occlusion changes
    # Create fully unoccluded depth map
    unoccluded_depth = np.full((720, 1280), np.inf, dtype=np.float32)
    unoccluded_depth[340:380, 600:680] = 15.0  # Larger unoccluded region

    visibility_unoccluded, _ = compute_visibility(
        box3d, simple_cam, unoccluded_depth, test_config, 'sphere', 0,
        False, None, 'test_obj', 'front'
    )

    # Unoccluded should have higher or equal visibility
    assert visibility_unoccluded >= visibility, \
        f"Unoccluded visibility ({visibility_unoccluded}) should be >= occluded ({visibility})"


def test_parameter_sensitivity():
    """Validate parameter sensitivity per vision-aware-annotations.md requirements"""
    # Create test setup
    front_cam = create_test_camera('front')
    vehicle = generate_test_vehicle(bearing=0, distance=15.0)
    depth_map = create_synthetic_depth_map(front_cam, [vehicle])
    box3d = {'surface_pts': vehicle['surface_pts']}
    
    # Test sphere_radius_m sensitivity
    radii = [0.1, 0.15, 0.2, 0.25, 0.3]
    radius_visibilities = []
    
    for radius in radii:
        config = {
            'visibility': {
                'tau_base_m': 0.4, 'tau_scale_per_m': 0.04, 'sphere_radius_m': radius,
                'occlusion_fraction_thr': 0.2
            }
        }
        visibility, _ = compute_visibility(
            box3d, front_cam, depth_map, config, 'sphere', 0,
            False, None, 'test_obj', 'front'
        )
        radius_visibilities.append(visibility)
    
    # Verify reasonable behavior: larger radius should generally increase or stabilize visibility
    # (more robust to noise, but may over-smooth)
    for i in range(1, len(radius_visibilities)):
        # Allow some variance but expect general trend
        assert radius_visibilities[i] >= radius_visibilities[i-1] - 0.1, \
            f"Visibility should not decrease significantly with larger radius: {radius_visibilities}"
    
    # Test tau_base_m sensitivity
    tau_bases = [0.1, 0.2, 0.3, 0.4, 0.5]
    tau_visibilities = []
    
    for tau_base in tau_bases:
        config = {
            'visibility': {
                'tau_base_m': tau_base, 'tau_scale_per_m': 0.04, 'sphere_radius_m': 0.15,
                'occlusion_fraction_thr': 0.2
            }
        }
        visibility, _ = compute_visibility(
            box3d, front_cam, depth_map, config, 'sphere', 0,
            False, None, 'test_obj', 'front'
        )
        tau_visibilities.append(visibility)
    
    # Larger tau should generally increase visibility (more tolerant of depth differences)
    for i in range(1, len(tau_visibilities)):
        assert tau_visibilities[i] >= tau_visibilities[i-1] - 0.1, \
            f"Visibility should not decrease significantly with larger tau: {tau_visibilities}"


def test_fov_soft_margin_effectiveness():
    """Test that soft margin reduces false rejections at FOV boundaries."""
    # Test soft margin functionality directly with known geometry
    cam_fov_rad = {'test_cam': {'h': math.radians(60.0), 'v': math.radians(40.0)}}

    # Identity transformation for simplicity
    T_base_cam = np.eye(4)

    # Create point just outside FOV boundary (31° from center, FOV half-angle is 30°)
    boundary_angle = math.radians(31)  # Just outside 30° half-angle
    boundary_point = (20.0 * math.sin(boundary_angle), 0.0, 20.0 * math.cos(boundary_angle))

    # Test without soft margin - should fail
    no_margin_ok, no_margin_reason = cam_fov_gate_point(
        boundary_point, T_base_cam, 'test_cam', cam_fov_rad, soft_margin_rad=0.0
    )

    # Test with soft margin - should pass
    with_margin_ok, with_margin_reason = cam_fov_gate_point(
        boundary_point, T_base_cam, 'test_cam', cam_fov_rad, soft_margin_rad=math.radians(2.0)
    )

    # Verify that soft margin helps
    assert not no_margin_ok, f"Point at 31° should be rejected without margin, but got: {no_margin_reason}"
    assert with_margin_ok, f"Point at 31° should be accepted with 2° margin, but got: {with_margin_reason}"

    # Test that corner-based gating can provide fallback

    # Create corners that might pass corner-based gating even if point fails
    corners = np.array([
        boundary_point,
        (boundary_point[0] - 1, boundary_point[1], boundary_point[2]),
        (boundary_point[0] + 1, boundary_point[1], boundary_point[2]),
        (boundary_point[0], boundary_point[1] - 1, boundary_point[2])
    ])

    corner_ok, corner_reason = cam_fov_gate_corners(
        corners, T_base_cam, 'test_cam', cam_fov_rad, soft_margin_rad=math.radians(2.0)
    )

    # At least one gating method should work with margin
    assert with_margin_ok or corner_ok, \
        f"Either point with margin ({with_margin_reason}) or corners ({corner_reason}) should pass"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
