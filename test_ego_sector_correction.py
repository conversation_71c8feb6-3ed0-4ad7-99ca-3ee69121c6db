#!/usr/bin/env python3
"""
Test script for ego sector correction functionality.

This script validates that the ego sector corrections are working properly
for the RoboBUS dataset camera naming issue.
"""

import yaml
import numpy as np
from pathlib import Path
from robobus_vis.config.camera_correction import create_camera_corrector
from robobus_vis.visibility.fov_filter import ego_sector_gate

def load_test_config():
    """Load the configuration with ego sector corrections."""
    config_path = Path(__file__).resolve().parent / 'configs' / 'default.yaml'
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def test_ego_sector_correction():
    """Test that ego sector corrections are applied correctly."""
    print("Testing Ego Sector Correction")
    print("=" * 50)
    
    # Load configuration
    config = load_test_config()
    
    # Create ego sector corrector
    ego_corrector = create_camera_corrector(config)
    
    if not ego_corrector.enabled:
        print("❌ Ego sector correction is not enabled in config")
        return False
    
    print(f"✅ Ego sector correction enabled")
    ego_corrector.log_correction_summary()
    
    # Test cases: objects in different positions
    test_cases = [
        {
            'name': 'Object behind vehicle (rear)',
            'position': (0.0, -10.0),  # Behind ego vehicle
            'expected_cameras': ['120_left', '120_back', '120_right'],  # Should be visible to rear cameras
            'unexpected_cameras': ['left_back', 'right_back']  # Should NOT be visible to front cameras
        },
        {
            'name': 'Object in front of vehicle',
            'position': (0.0, 10.0),   # In front of ego vehicle
            'expected_cameras': ['left_back', '60_front', '120_front', 'right_back'],  # Should be visible to front cameras
            'unexpected_cameras': ['120_left', '120_right']  # Should NOT be visible to rear cameras
        },
        {
            'name': 'Object to the left of vehicle',
            'position': (-10.0, 0.0),  # Left side of ego vehicle
            'expected_cameras': ['120_left', 'left_back'],  # Should be visible to left cameras
            'unexpected_cameras': ['120_right', 'right_back']  # Should NOT be visible to right cameras
        },
        {
            'name': 'Object to the right of vehicle',
            'position': (10.0, 0.0),   # Right side of ego vehicle
            'expected_cameras': ['120_right', 'right_back'],  # Should be visible to right cameras
            'unexpected_cameras': ['120_left', 'left_back']  # Should NOT be visible to left cameras
        }
    ]
    
    # Get original and corrected ego sectors
    original_sectors = config.get('ego_sector_map_deg', {})
    corrected_sectors = ego_corrector.get_corrected_ego_sectors_map()
    
    # Convert to radians for testing
    original_sectors_rad = {k: {kk: vv * np.pi / 180.0 for kk, vv in v.items()} 
                           for k, v in original_sectors.items()}
    corrected_sectors_rad = {k: {kk: vv * np.pi / 180.0 for kk, vv in v.items()} 
                            for k, v in corrected_sectors.items()}
    
    all_tests_passed = True
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        print(f"   Object position: {test_case['position']}")
        
        # Test with original sectors (should have issues)
        print("   Original sectors:")
        for cam_name in test_case['expected_cameras']:
            if cam_name in original_sectors_rad:
                in_sector, reason = ego_sector_gate(test_case['position'], cam_name, original_sectors_rad)
                print(f"     {cam_name}: {'✅' if in_sector else '❌'} {reason}")
        
        # Test with corrected sectors (should work better)
        print("   Corrected sectors:")
        for cam_name in test_case['expected_cameras']:
            if cam_name in corrected_sectors_rad:
                in_sector, reason = ego_sector_gate(test_case['position'], cam_name, corrected_sectors_rad, ego_corrector=ego_corrector)
                expected_result = cam_name in test_case['expected_cameras']
                if in_sector == expected_result:
                    print(f"     {cam_name}: ✅ {reason} (correct)")
                else:
                    print(f"     {cam_name}: ❌ {reason} (incorrect - expected {expected_result})")
                    all_tests_passed = False
    
    return all_tests_passed

def test_specific_corrections():
    """Test specific camera corrections that we know should be different."""
    print("\n" + "=" * 50)
    print("Testing Specific Camera Corrections")
    print("=" * 50)
    
    config = load_test_config()
    ego_corrector = create_camera_corrector(config)
    
    # Test specific corrections
    corrections_to_test = [
        ('120_left', 'Should now point to rear-left (135°)'),
        ('left_back', 'Should now point to front-left (45°)'),
        ('120_right', 'Should now point to rear-right (-135°)'),
        ('right_back', 'Should now point to front-right (-45°)')
    ]
    
    original_sectors = config.get('ego_sector_map_deg', {})
    corrected_sectors = ego_corrector.get_corrected_ego_sectors_map()
    
    for cam_name, description in corrections_to_test:
        print(f"\n📷 Camera: {cam_name}")
        print(f"   Description: {description}")
        
        if cam_name in original_sectors:
            orig = original_sectors[cam_name]
            print(f"   Original:  center={orig['center']}°, half={orig['half']}°")
        
        if cam_name in corrected_sectors:
            corr = corrected_sectors[cam_name]
            print(f"   Corrected: center={corr['center']}°, half={corr['half']}°")
            
            # Check if correction was actually applied
            if cam_name in original_sectors:
                if orig['center'] != corr['center']:
                    print(f"   ✅ Correction applied (center changed from {orig['center']}° to {corr['center']}°)")
                else:
                    print(f"   ⚠️  No correction applied (center unchanged)")
        
        # Get physical position note
        note = ego_corrector.get_physical_position_note(cam_name)
        if note:
            print(f"   Physical position: {note}")

if __name__ == "__main__":
    print("Ego Sector Correction Test Suite")
    print("=" * 60)
    
    try:
        # Test basic functionality
        basic_test_passed = test_ego_sector_correction()
        
        # Test specific corrections
        test_specific_corrections()
        
        print("\n" + "=" * 60)
        if basic_test_passed:
            print("✅ All basic tests passed!")
        else:
            print("❌ Some tests failed - check the output above")
        
        print("\n💡 Next steps:")
        print("   1. Run the batch processing pipeline to see improved visibility results")
        print("   2. Compare before/after visibility scores on the same dataset")
        print("   3. Validate that objects appear in the correct camera sectors")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()