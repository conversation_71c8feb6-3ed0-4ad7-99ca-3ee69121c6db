#!/usr/bin/env python3
"""
Integration test for safe configuration access patterns.

This test validates that the existing system components work correctly
with the new safe configuration access patterns.
"""

import sys
import os
import tempfile
import yaml
import numpy as np
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

from robobus_vis.config.loader import load_config, load_config_safe
from robobus_vis.visibility.dispatcher import compute_visibility
from robobus_vis.visibility.fusion import fuse_camera_visibility
from robobus_vis.visibility.conservative_validation import (
    get_conservative_config_safe,
    validate_conservative_config
)


def create_test_config():
    """Create a test configuration for integration testing."""
    return {
        'conservative_validation': {
            'enabled': True,
            'azimuth_tolerance_deg': 10.0,
            'min_depth_points': 5,
            'tau_multiplier': 1.5
        },
        'visibility': {
            'tau_base_m': 2.0,
            'tau_scale_per_m': 0.1,
            'sphere_radius_m': 0.15,
            'occlusion_fraction_thr': 0.2,
            'min_neighbors': 3,
            'treat_no_depth_as_visible': True,
            'max_window_px': 15,
            'enable_diagnostics': False,
            'diagnostic_output_dir': 'outputs/diagnostics'
        },
        'fov_filter': {
            'enabled': True,
            'use_corner_check': False,
            'vfov_deg_default': 50.0,
            'debug_filtering': False,
            'fallback_on_filter': True,
            'soft_margin_deg': 10.0
        },
        'camera_fovs_deg': {
            '60_front': {'h': 70.0, 'v': 50.0},
            '120_front': {'h': 130.0, 'v': 50.0}
        },
        'visibility_overrides': {
            '120_back': {
                'tau_base_m': 0.5,
                'sphere_radius_m': 0.22
            }
        }
    }


def create_mock_camera():
    """Create a mock camera object for testing."""
    class MockCamera:
        def __init__(self):
            self.fov_horizontal = 120.0
            self.fov_vertical = 50.0
            self.position = np.array([0, 0, 0])
            
        def project_3d_to_2d(self, points_3d):
            # Simple mock projection
            return points_3d[:, :2] * 100 + 500
            
        def get_fov_bounds(self):
            return {
                'h_min': -60.0, 'h_max': 60.0,
                'v_min': -25.0, 'v_max': 25.0
            }
    
    return MockCamera()


def test_config_loading_integration():
    """Test that configuration loading works with safe access patterns."""
    print("Testing configuration loading integration...")
    
    test_config = create_test_config()
    
    # Create temporary config file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(test_config, f)
        temp_config_path = f.name
    
    try:
        # Test normal loading
        config = load_config(temp_config_path)
        assert isinstance(config, dict)
        assert 'conservative_validation' in config
        assert 'visibility' in config
        
        # Test safe loading
        safe_config = load_config_safe(temp_config_path)
        assert isinstance(safe_config, dict)
        assert 'conservative_validation' in safe_config
        assert 'visibility' in safe_config
        
        print("✓ Configuration loading integration works")
        return True
        
    except Exception as e:
        print(f"✗ Configuration loading integration failed: {e}")
        return False
    finally:
        os.unlink(temp_config_path)


def test_conservative_validation_integration():
    """Test that conservative validation works with safe config access."""
    print("Testing conservative validation integration...")
    
    config = create_test_config()
    
    try:
        # Test safe config extraction
        cv_config = get_conservative_config_safe(config)
        assert isinstance(cv_config, dict)
        assert cv_config['enabled'] == True
        assert cv_config['azimuth_tolerance_deg'] == 10.0
        
        # Test validation
        is_valid, error_msg = validate_conservative_config(config)
        assert is_valid == True
        
        print("✓ Conservative validation integration works")
        return True
        
    except Exception as e:
        print(f"✗ Conservative validation integration failed: {e}")
        return False


def test_dispatcher_integration():
    """Test that dispatcher works with safe config access."""
    print("Testing dispatcher integration...")
    
    config = create_test_config()
    
    try:
        # Create test data
        box3d = {
            'x': 2.0, 'y': 0.0, 'z': 1.5,
            'l': 4.0, 'w': 2.0, 'h': 1.8,
            'yaw': 0.0
        }
        
        cam = create_mock_camera()
        depth_img = np.random.rand(100, 100) * 10  # Mock depth image
        
        # Test dispatcher with safe config access
        visibility, stats = compute_visibility(
            box3d=box3d,
            cam=cam,
            depth_img=depth_img,
            cfg=config,
            method_arg='sphere',
            samples=1000,
            enable_diagnostics=False,
            debug_dir=None,
            obj_id='test_obj',
            cam_name='120_front'
        )
        
        assert isinstance(visibility, (int, float))
        assert isinstance(stats, dict)
        assert 0.0 <= visibility <= 1.0
        
        print("✓ Dispatcher integration works")
        return True
        
    except Exception as e:
        print(f"✗ Dispatcher integration failed: {e}")
        return False


def test_fusion_integration():
    """Test that fusion works with safe config access."""
    print("Testing fusion integration...")
    
    try:
        # Test fusion with per-camera visibility scores
        per_cam = {
            '60_front': 0.8,
            '120_front': 0.6,
            '120_left': 0.4,
            '120_right': None,  # No FOV
            '120_back': 0.2
        }
        
        vis_avg, vis_max, visible_views, fusion_stats = fuse_camera_visibility(
            per_cam, 
            include_noFOV_as_zero=False,
            conservative_fusion=True
        )
        
        assert isinstance(vis_avg, (int, float))
        assert isinstance(vis_max, (int, float))
        assert isinstance(visible_views, list)
        assert isinstance(fusion_stats, dict)
        
        assert 0.0 <= vis_avg <= 1.0
        assert 0.0 <= vis_max <= 1.0
        assert vis_max >= vis_avg  # Max should be >= average
        
        print("✓ Fusion integration works")
        return True
        
    except Exception as e:
        print(f"✗ Fusion integration failed: {e}")
        return False


def test_invalid_config_handling():
    """Test that system handles invalid configurations gracefully."""
    print("Testing invalid configuration handling...")
    
    try:
        # Test with missing sections
        incomplete_config = {
            'visibility': {
                'tau_base_m': 2.0
                # Missing other required fields
            }
            # Missing conservative_validation section
        }
        
        # Should not crash and should provide defaults
        cv_config = get_conservative_config_safe(incomplete_config)
        assert isinstance(cv_config, dict)
        assert 'enabled' in cv_config
        assert 'azimuth_tolerance_deg' in cv_config
        
        # Test with invalid values
        invalid_config = {
            'conservative_validation': {
                'enabled': 'not_a_bool',
                'azimuth_tolerance_deg': -10,  # Invalid range
                'min_depth_points': 0,  # Invalid range
                'tau_multiplier': 100  # Invalid range
            }
        }
        
        cv_config = get_conservative_config_safe(invalid_config)
        assert isinstance(cv_config, dict)
        # Should use safe defaults for invalid values
        assert isinstance(cv_config['enabled'], bool)
        assert 0 <= cv_config['azimuth_tolerance_deg'] <= 45
        assert cv_config['min_depth_points'] >= 1
        assert 0.5 <= cv_config['tau_multiplier'] <= 5.0
        
        print("✓ Invalid configuration handling works")
        return True
        
    except Exception as e:
        print(f"✗ Invalid configuration handling failed: {e}")
        return False


def test_backward_compatibility():
    """Test that the system maintains backward compatibility."""
    print("Testing backward compatibility...")
    
    try:
        # Test with old-style configuration (no conservative_validation section)
        old_config = {
            'visibility': {
                'tau_base_m': 2.0,
                'tau_scale_per_m': 0.1,
                'sphere_radius_m': 0.15,
                'treat_no_depth_as_visible': True
            },
            'fov_filter': {
                'enabled': True,
                'soft_margin_deg': 2.0  # Old default
            }
        }
        
        # Should work without conservative validation
        cv_config = get_conservative_config_safe(old_config)
        assert cv_config['enabled'] == False  # Should default to disabled
        
        # Validation should pass
        is_valid, error_msg = validate_conservative_config(old_config)
        assert is_valid == True
        
        print("✓ Backward compatibility works")
        return True
        
    except Exception as e:
        print(f"✗ Backward compatibility failed: {e}")
        return False


def test_configuration_validation_startup():
    """Test startup configuration validation."""
    print("Testing configuration validation at startup...")
    
    try:
        from robobus_vis.config.safe_access import validate_configuration_startup
        
        # Test valid configuration
        valid_config = create_test_config()
        is_valid, critical_errors, warnings = validate_configuration_startup(valid_config)
        assert is_valid == True
        assert len(critical_errors) == 0
        
        # Test configuration that should generate warnings
        warning_config = create_test_config()
        warning_config['conservative_validation']['azimuth_tolerance_deg'] = 35.0  # Large tolerance
        warning_config['visibility']['tau_base_m'] = 8.0  # Large tau_base
        
        is_valid, critical_errors, warnings = validate_configuration_startup(warning_config)
        assert is_valid == True  # Should still be valid
        assert len(warnings) > 0  # Should have warnings
        
        print("✓ Configuration validation at startup works")
        return True
        
    except Exception as e:
        print(f"✗ Configuration validation at startup failed: {e}")
        return False


def main():
    """Run all integration tests."""
    print("Running safe configuration access integration tests...\n")
    
    tests = [
        test_config_loading_integration,
        test_conservative_validation_integration,
        test_dispatcher_integration,
        test_fusion_integration,
        test_invalid_config_handling,
        test_backward_compatibility,
        test_configuration_validation_startup
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            result = test()
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print(f"Integration Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("✓ All integration tests passed!")
        return True
    else:
        print("✗ Some integration tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)