sync:
  enable: false
  max_sync_dt_ms: 30
  pose_required: true

visibility:
  # Sphere-only configuration
  tau_base_m: 0.4  # Base tolerance for depth comparison (meters)
  tau_scale_per_m: 0.05  # Distance-dependent tolerance scaling
  sphere_radius_m: 0.08
  occlusion_fraction_thr: 0.12
  min_neighbors: 3
  treat_no_depth_as_visible: false  # Enhanced: Assume visibility when depth is insufficient
  max_window_px: 15
  # Enable diagnostic mode for debugging
  enable_diagnostics: false
  diagnostic_output_dir: "outputs/diagnostics"

# Conservative validation configuration for enhanced visibility calculation
conservative_validation:
  enabled: false  # Enable/disable conservative validation (default: false for backward compatibility)
  azimuth_tolerance_deg: 10.0  # FOV expansion tolerance in degrees (default: 10.0)
  min_depth_points: 5  # Minimum depth points to trust depth data (default: 5)
  tau_multiplier: 1.5  # Multiplier for depth tolerance parameters (default: 1.5)
visibility_overrides:
  120_back:   { tau_base_m: 0.3, tau_scale_per_m: 0.05, sphere_radius_m: 0.22 }
  120_left:   { tau_base_m: 0.3, tau_scale_per_m: 0.05, sphere_radius_m: 0.20 }
  120_right:  { tau_base_m: 0.3, tau_scale_per_m: 0.05, sphere_radius_m: 0.20 }
  left_back:  { tau_base_m: 0.3, tau_scale_per_m: 0.05, sphere_radius_m: 0.22 }
  right_back: { tau_base_m: 0.3, tau_scale_per_m: 0.05, sphere_radius_m: 0.22 }


fov_filter:
  enabled: true
  use_corner_check: false
  vfov_deg_default: 50.0  # INCREASED: More permissive vertical FOV
  debug_filtering: true  # NEW: Enable debug logging for filtering
  fallback_on_filter: true  # NEW: Allow fallback when filtering is too aggressive
  soft_margin_deg: 10.0  # Enhanced: Increased default for loose gating (conservative validation)

# RELAXED CAMERA FOVs: Increased to prevent over-filtering
camera_fovs_deg:
  60_front:   {h: 70.0,  v: 50.0}   # INCREASED: from 60°/40° to 70°/50°
  120_front:  {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  120_back:   {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  120_left:   {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  120_right:  {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  left_back:  {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°
  right_back: {h: 130.0, v: 50.0}   # INCREASED: from 120°/40° to 130°/50°

# RELAXED EGO SECTORS: Increased to prevent over-filtering
ego_sector_map_deg:
  60_front:   {center:   0.0, half: 45.0}   # INCREASED: from 30° to 45°
  120_front:  {center:   0.0, half: 75.0}   # INCREASED: from 60° to 75°
  120_left:   {center:  90.0, half: 75.0}   # INCREASED: from 60° to 75°
  120_right:  {center: -90.0, half: 75.0}   # INCREASED: from 60° to 75°
  120_back:   {center: 180.0, half: 75.0}   # INCREASED: from 60° to 75°
  left_back:  {center: 150.0, half: 75.0}   # INCREASED: from 60° to 75°
  right_back: {center: -150.0, half: 75.0}  # INCREASED: from 60° to 75°

training_filter:
  drop_if_level_ge: 4
  min_vis_avg: 0.3  # Lower threshold to include more training samples

camera_map:
  60_front: H60L-E08160504
  120_front: H120L-E06170513
  120_back: H120L-E06170599
  120_left: H120L-F05160609
  120_right: H120L-E12190550
  left_back: H120L-P05160612
  right_back: H120L-F05160614

# Ego Sector Correction for RoboBUS Dataset Camera Positioning
# Note: Camera calibrations in camera_map are CORRECT and should not be changed
ego_sector_correction:
  enabled: true  # Enable/disable ego sector correction
  
  # Corrected ego sectors based on actual camera physical positions
  # (keeping directory names but fixing the angular sectors)
  corrected_ego_sectors_deg:
    # These cameras need correction based on actual physical positions:
    "120_left":    {center: 135.0, half: 75.0}   # Actually rear-left (was 90°)
    "left_back":   {center:  45.0, half: 75.0}   # Actually front-left (was 150°)
    "120_right":   {center: -135.0, half: 75.0}  # Actually rear-right (was -90°)
    "right_back":  {center: -45.0, half: 75.0}   # Actually front-right (was -150°)
    
    # These cameras are correctly positioned (included for completeness):
    "60_front":    {center:   0.0, half: 45.0}   # Front center (unchanged)
    "120_front":   {center:   0.0, half: 75.0}   # Front center wide (unchanged)
    "120_back":    {center: 180.0, half: 75.0}   # Rear center (unchanged)
  
  # Optional: Logging mapping for clarity
  physical_position_notes:
    "120_left": "rear_left_camera"      # Directory name vs actual position
    "left_back": "front_left_camera"    # Directory name vs actual position
    "120_right": "rear_right_camera"    # Directory name vs actual position
    "right_back": "front_right_camera"  # Directory name vs actual position

# SIMPLIFIED CONFIGURATION - Removed complex features for core functionality focus
