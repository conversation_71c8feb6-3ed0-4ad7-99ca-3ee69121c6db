"""
Configuration loader with validation for conservative validation parameters.

This module provides a centralized configuration loading function that includes
validation and applies safe defaults for conservative validation.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional

from .validation import (
    validate_full_config, 
    apply_conservative_defaults
)
from .safe_access import (
    get_safe_config_value,
    get_conservative_validation_config,
    validate_configuration_startup,
    create_minimal_config,
    handle_configuration_degradation,
    log_configuration_access_summary
)

logger = logging.getLogger(__name__)


def load_config(config_path: str, validate: bool = True) -> Dict[str, Any]:
    """
    Load configuration from YAML file with validation and safe defaults.
    
    Args:
        config_path: Path to the YAML configuration file
        validate: Whether to perform configuration validation (default: True)
        
    Returns:
        Configuration dictionary with validated parameters and safe defaults
        
    Raises:
        FileNotFoundError: If configuration file doesn't exist
        yaml.YAMLError: If YAML parsing fails
        ValueError: If configuration validation fails with critical errors
    """
    config_path = Path(config_path)
    
    if not config_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        logger.error(f"Failed to parse YAML configuration: {e}")
        raise
    
    if config is None:
        config = {}
    
    # Apply conservative defaults
    config = apply_conservative_defaults(config)
    
    # Validate configuration if requested
    if validate:
        try:
            # Use new startup validation
            is_valid, critical_errors, warnings = validate_configuration_startup(config)
            
            # Log warnings
            for warning in warnings:
                logger.warning(f"Configuration warning: {warning}")
            
            # Handle critical errors
            if not is_valid:
                error_msg = f"Configuration validation failed:\n" + "\n".join(f"  - {error}" for error in critical_errors)
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # Fallback to legacy validation for backward compatibility
            legacy_valid, legacy_errors = validate_full_config(config)
            if not legacy_valid:
                for error in legacy_errors:
                    logger.warning(f"Legacy validation warning: {error}")
                    
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            # Implement graceful degradation
            config = handle_configuration_degradation(config, e)
            logger.warning("Using degraded configuration - some features may be disabled")
    
    # Log configuration status using safe access
    log_configuration_access_summary(config)
    
    return config


def load_config_safe(config_path: str, fallback_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Load configuration with graceful error handling.
    
    Args:
        config_path: Path to the YAML configuration file
        fallback_config: Fallback configuration to use if loading fails
        
    Returns:
        Configuration dictionary, fallback config, or minimal defaults
    """
    try:
        return load_config(config_path, validate=True)
    except Exception as e:
        logger.error(f"Failed to load configuration from {config_path}: {e}")
        
        if fallback_config is not None:
            logger.info("Using provided fallback configuration")
            try:
                # Apply safe defaults to fallback config
                safe_fallback = apply_conservative_defaults(fallback_config.copy())
                # Validate the fallback config
                is_valid, critical_errors, warnings = validate_configuration_startup(safe_fallback)
                if not is_valid:
                    logger.warning("Fallback configuration is invalid, using minimal defaults")
                    return create_minimal_config()
                return safe_fallback
            except Exception as fallback_error:
                logger.error(f"Fallback configuration also failed: {fallback_error}")
        
        # Create minimal working configuration using safe access utilities
        logger.warning("Using minimal default configuration")
        return create_minimal_config()


def get_conservative_validation_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract conservative validation configuration with safe defaults.
    
    Args:
        config: Full configuration dictionary
        
    Returns:
        Conservative validation configuration with all required parameters
    """
    cv_config = config.get('conservative_validation', {})
    
    return {
        'enabled': get_safe_config_value(config, 'conservative_validation.enabled', False),
        'azimuth_tolerance_deg': get_safe_config_value(config, 'conservative_validation.azimuth_tolerance_deg', 10.0),
        'min_depth_points': get_safe_config_value(config, 'conservative_validation.min_depth_points', 5),
        'tau_multiplier': get_safe_config_value(config, 'conservative_validation.tau_multiplier', 1.5)
    }


def is_conservative_validation_enabled(config: Dict[str, Any]) -> bool:
    """
    Check if conservative validation is enabled in the configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        True if conservative validation is enabled, False otherwise
    """
    return get_safe_config_value(config, 'conservative_validation.enabled', False)


def log_configuration_summary(config: Dict[str, Any]) -> None:
    """
    Log a summary of the current configuration for debugging.
    
    Args:
        config: Configuration dictionary
    """
    logger.info("=== Configuration Summary ===")
    
    # Conservative validation settings
    cv_enabled = is_conservative_validation_enabled(config)
    logger.info(f"Conservative Validation: {'ENABLED' if cv_enabled else 'DISABLED'}")
    
    if cv_enabled:
        cv_config = get_conservative_validation_config(config)
        logger.info(f"  Azimuth Tolerance: {cv_config['azimuth_tolerance_deg']}°")
        logger.info(f"  Min Depth Points: {cv_config['min_depth_points']}")
        logger.info(f"  Tau Multiplier: {cv_config['tau_multiplier']}")
    
    # FOV filter settings
    fov_enabled = get_safe_config_value(config, 'fov_filter.enabled', True)
    logger.info(f"FOV Filter: {'ENABLED' if fov_enabled else 'DISABLED'}")
    if fov_enabled:
        soft_margin = get_safe_config_value(config, 'fov_filter.soft_margin_deg', 2.0)
        logger.info(f"  Soft Margin: {soft_margin}°")
    
    # Visibility settings
    treat_no_depth = get_safe_config_value(config, 'visibility.treat_no_depth_as_visible', True)
    logger.info(f"Treat No Depth as Visible: {treat_no_depth}")
    
    tau_base = get_safe_config_value(config, 'visibility.tau_base_m', 2.0)
    tau_scale = get_safe_config_value(config, 'visibility.tau_scale_per_m', 0.1)
    logger.info(f"Tau Parameters: base={tau_base}m, scale={tau_scale}")
    
    logger.info("=== End Configuration Summary ===")