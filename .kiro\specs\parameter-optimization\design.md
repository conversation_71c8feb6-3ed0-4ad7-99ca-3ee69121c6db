# Parameter Optimization Design Document

## Overview

This design addresses the critical parameter tuning issues in the VisionAware Annotations project, focusing on eliminating excessive tau_base_m warnings and optimizing visibility calculations for pure vision perception model training. The solution implements distance-based parameter adaptation, camera-specific optimization, and enhanced multi-camera fusion strategies.

## Architecture

### Core Design Principles

1. **Distance-Adaptive Parameters**: Visibility parameters automatically adjust based on object distance from ego vehicle
2. **Camera-Specific Optimization**: Each camera uses parameters tuned for its mounting position and characteristics  
3. **Conservative Multi-Camera Fusion**: Prioritize visibility preservation across camera views
4. **Performance-Aware Configuration**: Balance accuracy with processing efficiency
5. **Diagnostic-Rich Processing**: Provide clear insights into parameter decisions and system behavior

## Components and Interfaces

### 2.1 Distance-Based Parameter Adapter

**Purpose**: Automatically adjust visibility parameters based on object distance and importance

**Key Functions**:
- `get_distance_adaptive_params(object_distance, camera_name, base_config)`: Returns optimized parameters for specific object-camera combinations
- `calculate_pixel_importance(bbox_2d, image_resolution)`: Determines object importance based on pixel area
- `apply_distance_weighting(visibility_score, distance, pixel_area)`: Applies distance-based weighting to final scores

**Parameter Adaptation Logic**:
```python
def get_distance_adaptive_params(distance_m, camera_name, base_config):
    """
    Adapt parameters based on object distance and camera characteristics.
    
    Distance Zones:
    - Close (0-30m): High precision, strict thresholds
    - Medium (30-60m): Balanced accuracy and performance  
    - Far (60m+): Relaxed thresholds, noise tolerance
    """
    if distance_m <= 30.0:
        # Close range: High precision for safety-critical objects
        tau_base_m = min(0.3, base_config.get('tau_base_m', 0.3))
        sphere_radius_m = 0.10
        occlusion_fraction_thr = 0.15
    elif distance_m <= 60.0:
        # Medium range: Balanced approach
        tau_base_m = min(0.6, base_config.get('tau_base_m', 0.6))
        sphere_radius_m = 0.15
        occlusion_fraction_thr = 0.20
    else:
        # Far range: Relaxed for distant objects
        tau_base_m = min(1.0, base_config.get('tau_base_m', 1.0))
        sphere_radius_m = 0.25
        occlusion_fraction_thr = 0.30
    
    # Apply camera-specific adjustments
    camera_multiplier = get_camera_multiplier(camera_name)
    tau_base_m *= camera_multiplier
    
    return {
        'tau_base_m': tau_base_m,
        'sphere_radius_m': sphere_radius_m,
        'occlusion_fraction_thr': occlusion_fraction_thr
    }
```

### 2.2 Camera-Specific Parameter Manager

**Purpose**: Manage optimal parameters for each camera based on mounting position and characteristics

**Configuration Schema**:
```yaml
camera_parameter_profiles:
  # Front cameras: High resolution, close-range focus
  front_profile:
    cameras: ["60_front", "120_front"]
    tau_base_multiplier: 0.7  # More strict for front-facing
    sphere_radius_multiplier: 0.8
    recommended_tau_max: 0.4
    
  # Side cameras: Medium range coverage
  side_profile:
    cameras: ["120_left", "120_right", "left_back", "right_back"]
    tau_base_multiplier: 1.0  # Standard parameters
    sphere_radius_multiplier: 1.0
    recommended_tau_max: 0.6
    
  # Rear camera: Parking/reversing scenarios
  rear_profile:
    cameras: ["120_back"]
    tau_base_multiplier: 0.85  # Slightly more strict for reversing
    sphere_radius_multiplier: 0.9
    recommended_tau_max: 0.5
```

**Key Functions**:
- `get_camera_profile(camera_name)`: Returns parameter profile for specific camera
- `validate_camera_parameters(camera_name, params)`: Validates parameters against camera limits
- `get_recommended_tau_max(camera_name)`: Returns maximum recommended tau_base_m for camera

### 2.3 Enhanced Multi-Camera Fusion

**Purpose**: Implement conservative fusion strategy that preserves visibility information

**Fusion Strategy**:
```python
def enhanced_camera_fusion(per_camera_visibility, per_camera_stats, fusion_config):
    """
    Enhanced fusion prioritizing visibility preservation.
    
    Strategy:
    1. Conservative Maximum: Take max visibility if any camera shows clear visibility (>0.7)
    2. Weighted Average: For moderate visibility, use distance-weighted average
    3. Confidence Weighting: Weight by camera confidence and object pixel area
    """
    valid_scores = [(cam, score) for cam, score in per_camera_visibility.items() 
                   if score is not None]
    
    if not valid_scores:
        return 0.0, 0.0, []
    
    # Extract scores and apply conservative logic
    scores = [score for _, score in valid_scores]
    max_score = max(scores)
    
    # Conservative fusion: If any camera shows high visibility, preserve it
    if max_score >= 0.7:
        fused_score = max_score
        confidence = 0.9  # High confidence when clearly visible
    else:
        # Weighted average for moderate visibility
        weights = [get_camera_weight(cam, per_camera_stats.get(cam, {})) 
                  for cam, _ in valid_scores]
        fused_score = np.average(scores, weights=weights)
        confidence = 0.6  # Moderate confidence
    
    # Determine visible views with adaptive threshold
    adaptive_threshold = max(0.15, fused_score * 0.3)
    visible_views = [cam for cam, score in valid_scores 
                    if score >= adaptive_threshold]
    
    return fused_score, confidence, visible_views
```

### 2.4 Parameter Warning and Diagnostic System

**Purpose**: Provide clear diagnostic information and eliminate excessive warnings

**Warning Management**:
- **Adaptive Thresholds**: Warnings only when parameters exceed camera-specific limits by >50%
- **Batched Logging**: Group similar warnings and report statistics instead of individual instances
- **Recommendation Engine**: Suggest optimal parameters when warnings occur

**Diagnostic Output**:
```python
class ParameterDiagnostics:
    def __init__(self):
        self.parameter_usage = defaultdict(list)
        self.warning_counts = defaultdict(int)
        self.performance_stats = {}
    
    def log_parameter_usage(self, camera, object_id, distance, params_used):
        """Log parameter usage for analysis."""
        self.parameter_usage[camera].append({
            'object_id': object_id,
            'distance': distance,
            'tau_base_m': params_used['tau_base_m'],
            'sphere_radius_m': params_used['sphere_radius_m'],
            'timestamp': time.time()
        })
    
    def generate_summary_report(self):
        """Generate summary of parameter usage and recommendations."""
        report = {
            'total_objects_processed': sum(len(objs) for objs in self.parameter_usage.values()),
            'parameter_warnings': dict(self.warning_counts),
            'camera_statistics': {},
            'recommendations': []
        }
        
        for camera, usage_data in self.parameter_usage.items():
            if usage_data:
                distances = [obj['distance'] for obj in usage_data]
                tau_values = [obj['tau_base_m'] for obj in usage_data]
                
                report['camera_statistics'][camera] = {
                    'objects_processed': len(usage_data),
                    'avg_distance': np.mean(distances),
                    'avg_tau_base_m': np.mean(tau_values),
                    'distance_range': [min(distances), max(distances)]
                }
        
        return report
```

## Data Models

### 2.5 Enhanced Configuration Schema

```yaml
# Enhanced parameter configuration with distance adaptation
visibility_parameters:
  # Distance-based parameter zones
  distance_zones:
    close_range:
      max_distance_m: 30.0
      tau_base_m: 0.3
      sphere_radius_m: 0.10
      occlusion_fraction_thr: 0.15
      priority_weight: 1.0
      
    medium_range:
      max_distance_m: 60.0
      tau_base_m: 0.6
      sphere_radius_m: 0.15
      occlusion_fraction_thr: 0.20
      priority_weight: 0.7
      
    far_range:
      max_distance_m: 200.0
      tau_base_m: 1.0
      sphere_radius_m: 0.25
      occlusion_fraction_thr: 0.30
      priority_weight: 0.4

  # Camera-specific parameter profiles
  camera_profiles:
    front_cameras:
      cameras: ["60_front", "120_front"]
      tau_multiplier: 0.7
      sphere_multiplier: 0.8
      max_recommended_tau: 0.4
      
    side_cameras:
      cameras: ["120_left", "120_right", "left_back", "right_back"]
      tau_multiplier: 1.0
      sphere_multiplier: 1.0
      max_recommended_tau: 0.6
      
    rear_cameras:
      cameras: ["120_back"]
      tau_multiplier: 0.85
      sphere_multiplier: 0.9
      max_recommended_tau: 0.5

  # Pixel importance weighting
  pixel_importance:
    enabled: true
    min_pixel_area: 100  # Minimum area to consider
    high_importance_threshold: 1000  # Pixels for high importance
    importance_weight_factor: 1.5  # Multiplier for important objects

# Enhanced fusion configuration
multi_camera_fusion:
  strategy: "conservative_maximum"  # Options: conservative_maximum, weighted_average, hybrid
  high_visibility_threshold: 0.7  # Threshold for conservative preservation
  confidence_weighting: true
  adaptive_visible_threshold: true
  
# Warning and diagnostic configuration
diagnostics:
  warning_management:
    enabled: true
    batch_warnings: true  # Group similar warnings
    max_warnings_per_camera: 5  # Limit warnings per camera per run
    warning_threshold_multiplier: 1.5  # Only warn when exceeding by this factor
    
  performance_monitoring:
    enabled: true
    log_parameter_usage: true
    generate_summary_reports: true
    save_diagnostic_files: true
```

## Error Handling

### 2.6 Robust Parameter Validation

**Startup Validation**:
- Validate all parameter ranges against camera capabilities
- Check for conflicting configuration combinations
- Provide clear error messages with suggested fixes

**Runtime Error Handling**:
- Graceful fallback to safe default parameters when invalid values detected
- Automatic parameter adjustment when warnings exceed thresholds
- Comprehensive logging of all parameter decisions and adjustments

**Configuration Recovery**:
```python
def validate_and_recover_config(config):
    """
    Validate configuration and provide automatic recovery for common issues.
    """
    issues = []
    fixes = []
    
    # Check tau_base_m values against camera limits
    for camera in config.get('camera_map', {}).keys():
        max_tau = get_camera_max_tau(camera)
        current_tau = get_camera_tau(config, camera)
        
        if current_tau > max_tau * 1.2:  # 20% tolerance
            issues.append(f"tau_base_m={current_tau:.1f} too high for {camera}")
            fixes.append(f"Recommend tau_base_m<={max_tau:.1f} for {camera}")
            
            # Auto-fix if enabled
            if config.get('auto_fix_parameters', False):
                set_camera_tau(config, camera, max_tau)
                fixes.append(f"Auto-corrected {camera} tau_base_m to {max_tau:.1f}")
    
    return config, issues, fixes
```

## Testing Strategy

### 2.7 Comprehensive Testing Approach

**Unit Tests**:
- Distance-based parameter adaptation logic
- Camera-specific parameter selection
- Multi-camera fusion algorithms
- Warning and diagnostic systems

**Integration Tests**:
- End-to-end pipeline with optimized parameters
- Performance benchmarking against baseline
- Accuracy validation against ground truth annotations

**Validation Tests**:
- Process visibility_demo_data/clip_dataset_1 with < 10 warnings
- Verify distance-based parameter adaptation
- Confirm multi-camera fusion preserves visibility
- Validate diagnostic output completeness

This design provides a comprehensive solution to the parameter optimization challenges while maintaining the core project objectives of accurate visibility annotation for pure vision perception model training.