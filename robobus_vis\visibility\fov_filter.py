import numpy as np
from ..config.camera_correction import EgoSectorCorrector

DEG = np.pi / 180.0

def wrap_pi(a):
    return (a + np.pi) % (2*np.pi) - np.pi

def ang_diff(a, b):
    return wrap_pi(a - b)


def in_sector(theta, center, half_width):
    return abs(ang_diff(theta, center)) <= half_width


def ego_sector_gate(center_ego, cam_name, sectors, ego_corrector=None):
    """
    Check if object is within camera's ego sector with optional correction.
    
    Args:
        center_ego: Object center in ego coordinates
        cam_name: Camera directory name
        sectors: Ego sector configuration
        ego_corrector: Optional EgoSectorCorrector for orientation fixes
    """
    x, y = center_ego[0], center_ego[1]
    if x == 0.0 and y == 0.0:
        return False, "object_at_origin"
    
    theta = np.arctan2(y, x)
    
    # Use corrected sectors if available
    if ego_corrector and ego_corrector.enabled:
        corrected_sectors = ego_corrector.get_corrected_ego_sectors_map()
        s = corrected_sectors.get(cam_name)
    else:
        s = sectors.get(cam_name)
    
    if s is None:
        return True, "no_sector_cfg"
    
    return in_sector(theta, s["center"] * DEG, s["half"] * DEG), "ok"


def cam_fov_gate_point(center_ego, T_base_cam, cam_name, cam_fov_rad, z_forward_positive=True, soft_margin_rad: float = 0.0, ego_corrector=None):
    """
    Check if object center is within camera FOV.
    
    Note: FOV parameters typically don't need correction as they're based on 
    camera intrinsics, not ego positioning. This parameter is kept for consistency.
    
    Args:
        center_ego: Object center in ego coordinates
        T_base_cam: Camera transformation matrix
        cam_name: Camera directory name
        cam_fov_rad: FOV configuration in radians
        z_forward_positive: Whether positive Z is forward
        soft_margin_rad: Additional margin in radians
        ego_corrector: Optional EgoSectorCorrector (unused for FOV)
    """
    P_h = np.array([center_ego[0], center_ego[1], center_ego[2], 1.0], dtype=float)
    Pc = (T_base_cam @ P_h)[:3]
    Xc, Yc, Zc = Pc
    if z_forward_positive and Zc <= 0.0:
        return False, "behind_camera"
    
    theta = np.arctan2(Xc, Zc)
    phi = np.arctan2(Yc, Zc)
    
    # Use standard FOV configuration (no correction needed for intrinsic FOV)
    f = cam_fov_rad.get(cam_name)
    
    if f is None:
        return True, "no_fov_cfg"
    
    h_half = f["h"] * 0.5 + soft_margin_rad
    v_half = f["v"] * 0.5 + soft_margin_rad
    
    if abs(theta) > h_half:
        return False, "outside_hfov"
    if abs(phi) > v_half:
        return False, "outside_vfov"
    
    return True, "ok"


def cam_fov_gate_corners(corners_ego, T_base_cam, cam_name, cam_fov_rad, z_forward_positive=True, soft_margin_rad: float = 0.0, ego_corrector=None):
    """
    Check if object corners are within camera FOV.
    
    Args:
        corners_ego: Object corners in ego coordinates
        T_base_cam: Camera transformation matrix
        cam_name: Camera directory name
        cam_fov_rad: FOV configuration in radians
        z_forward_positive: Whether positive Z is forward
        soft_margin_rad: Additional margin in radians
        ego_corrector: Optional EgoSectorCorrector (unused for FOV)
    """
    P = np.c_[corners_ego, np.ones((corners_ego.shape[0],1))]
    Pc = (T_base_cam @ P.T).T[:, :3]
    Xc, Yc, Zc = Pc[:,0], Pc[:,1], Pc[:,2]
    if z_forward_positive and not np.any(Zc > 0):
        return False, "all_behind"
    
    theta = np.arctan2(Xc[Zc>0], Zc[Zc>0])
    phi   = np.arctan2(Yc[Zc>0], Zc[Zc>0])
    if theta.size == 0:
        return False, "no_forward_points"
    
    # Use standard FOV configuration (no correction needed for intrinsic FOV)
    f = cam_fov_rad.get(cam_name)
    
    if f is None:
        return True, "no_fov_cfg"
    
    h_half = f["h"] * 0.5 + soft_margin_rad
    v_half = f["v"] * 0.5 + soft_margin_rad
    
    if (np.any(np.abs(theta) <= h_half) and np.any(np.abs(phi) <= v_half)):
        return True, "ok"
    
    return False, "outside_by_corners"
