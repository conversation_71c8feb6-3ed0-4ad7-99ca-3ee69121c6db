import argparse
from pathlib import Path
import yaml
from ..io.dataset_loader import ClipDataset
from ..io.calib_parser import parse_calibrated_sensor_pb_txt
from ..io.pose_loader import PoseStream

# Updated CLI: iterate all timestamps in a clip, just print basic stats for now.

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--clip_dir', required=True)
    parser.add_argument('--config', default=str(Path(__file__).resolve().parents[2] / 'configs' / 'default.yaml'))
    args = parser.parse_args()

    # Load configuration with validation
    from ..config.loader import load_config
    cfg = load_config(args.config, validate=True)
    clip = ClipDataset(args.clip_dir)
    ts_list = clip.list_timestamps()

    print('Loaded config keys:', list(cfg.keys()))
    print(f'Found {len(ts_list)} timestamps')

    # Load calibrations & pose stream (for later stages)
    calibs = parse_calibrated_sensor_pb_txt(str(clip.calib_pb_txt))
    pose = PoseStream(str(clip.pose_txt))
    print('Cameras parsed:', calibs.cameras())
    print('Pose health:', pose.health())

    # Iterate frames (no heavy compute yet)
    for i, ts in enumerate(ts_list[:5]):  # show first 5 as sample
        paths = clip.frame_paths_by_ts(ts)
        print(f'[{i}] ts={ts}: pcd={paths["pcd"].name}, json={paths["json"].name}, imgs={len(paths["images"])})')

if __name__ == '__main__':
    main()
