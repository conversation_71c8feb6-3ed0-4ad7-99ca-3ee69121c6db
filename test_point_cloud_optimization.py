#!/usr/bin/env python3
"""
Test script to validate point cloud optimization performance improvements.
"""

import sys
import time
import numpy as np
from pathlib import Path

# Add project root to path
sys.path.append('.')

from robobus_vis.geometry.depth_tools import pointcloud_to_depth, cull_points_by_camera
from robobus_vis.calib.camera_model import Camera

def create_test_camera():
    """Create a simple test camera for performance testing."""
    K = np.array([
        [500.0, 0.0, 320.0],
        [0.0, 500.0, 240.0],
        [0.0, 0.0, 1.0]
    ])
    
    T_base_cam = np.eye(4)
    
    return Camera(
        name="test_cam",
        K=K,
        dist=np.zeros(5),
        width=640,
        height=480,
        T_base_cam=T_base_cam
    )

def generate_test_point_cloud(num_points=50000):
    """Generate a large test point cloud for performance testing."""
    # Generate points in a 100m x 100m x 10m area around the vehicle
    x = np.random.uniform(-50, 50, num_points)
    y = np.random.uniform(-50, 50, num_points)
    z = np.random.uniform(-2, 8, num_points)
    
    return np.column_stack([x, y, z])

def test_spatial_filtering_performance():
    """Test the performance improvement from spatial filtering."""
    print("Testing Point Cloud Spatial Filtering Performance")
    print("=" * 60)
    
    # Create test data
    cam = create_test_camera()
    pts = generate_test_point_cloud(50000)
    
    print(f"Original point cloud size: {len(pts)} points")
    
    # Test different cameras
    test_cameras = ["120_left", "left_back", "120_right", "right_back", "120_front", "120_back"]
    
    for camera_name in test_cameras:
        print(f"\nTesting camera: {camera_name}")
        
        # Test spatial filtering
        mask = cull_points_by_camera(pts, camera_name)
        filtered_pts = pts[mask]
        reduction = (1 - len(filtered_pts) / len(pts)) * 100
        
        print(f"  Spatial filtering: {len(pts)} -> {len(filtered_pts)} points ({reduction:.1f}% reduction)")
        
        # Test performance without filtering
        start_time = time.time()
        D1, hits1, _ = pointcloud_to_depth(pts, cam, 480, 640)
        time_without = time.time() - start_time
        
        # Test performance with filtering
        start_time = time.time()
        D2, hits2, _ = pointcloud_to_depth(pts, cam, 480, 640, camera_name=camera_name)
        time_with = time.time() - start_time
        
        speedup = time_without / time_with if time_with > 0 else float('inf')
        
        print(f"  Performance without filtering: {time_without:.3f}s ({hits1} hits)")
        print(f"  Performance with filtering: {time_with:.3f}s ({hits2} hits)")
        print(f"  Speedup: {speedup:.1f}x")

def test_filtering_accuracy():
    """Test that spatial filtering doesn't significantly affect accuracy."""
    print("\nTesting Spatial Filtering Accuracy")
    print("=" * 40)
    
    cam = create_test_camera()
    
    # Create a smaller, more controlled point cloud
    # Points in different quadrants
    pts_front_left = np.array([[10, 5, 0], [15, 8, 1], [20, 10, 2]])
    pts_front_right = np.array([[10, -5, 0], [15, -8, 1], [20, -10, 2]])
    pts_rear_left = np.array([[-10, 5, 0], [-15, 8, 1], [-20, 10, 2]])
    pts_rear_right = np.array([[-10, -5, 0], [-15, -8, 1], [-20, -10, 2]])
    
    all_pts = np.vstack([pts_front_left, pts_front_right, pts_rear_left, pts_rear_right])
    
    print(f"Test point cloud: {len(all_pts)} points in 4 quadrants")
    
    # Test each camera's filtering
    cameras_and_expected = [
        ("left_back", "front-left"),  # Should keep front-left points
        ("right_back", "front-right"),  # Should keep front-right points
        ("120_left", "rear-left"),  # Should keep rear-left points
        ("120_right", "rear-right"),  # Should keep rear-right points
    ]
    
    for camera_name, expected_quadrant in cameras_and_expected:
        mask = cull_points_by_camera(all_pts, camera_name)
        kept_points = all_pts[mask]
        
        print(f"  {camera_name} (expects {expected_quadrant}): kept {len(kept_points)}/{len(all_pts)} points")
        
        if len(kept_points) > 0:
            print(f"    Kept points range: X=[{kept_points[:,0].min():.1f}, {kept_points[:,0].max():.1f}], "
                  f"Y=[{kept_points[:,1].min():.1f}, {kept_points[:,1].max():.1f}]")

if __name__ == "__main__":
    test_spatial_filtering_performance()
    test_filtering_accuracy()
    print("\nPoint cloud optimization test completed!")