#!/usr/bin/env python3
"""
Comprehensive verification test for Task 2: Enhanced visibility dispatcher integration.
"""

import sys
import numpy as np
import yaml
from unittest.mock import Mock

# Add project root to path
sys.path.append('.')

from robobus_vis.visibility.dispatcher import compute_visibility


def create_test_camera():
    """Create a test camera for verification."""
    cam = Mock()
    cam.T_base_cam = np.eye(4)
    
    def mock_project(points):
        n_points = len(points) if hasattr(points, '__len__') else 1
        result = []
        for i in range(n_points):
            u = 320 + np.random.uniform(-30, 30)
            v = 240 + np.random.uniform(-30, 30)
            z = 3.0 + np.random.uniform(-0.5, 0.5)
            result.append([u, v, z])
        return np.array(result)
    
    cam.project = mock_project
    return cam


def test_requirement_1_1_1_2():
    """Test Requirements 1.1, 1.2: Conservative validation with environmental occlusions."""
    print("Testing Requirements 1.1, 1.2: Conservative validation for environmental occlusions...")
    
    with open('configs/default.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Enable conservative validation
    config['conservative_validation']['enabled'] = True
    config['conservative_validation']['min_depth_points'] = 3
    
    # Test case: Object partially occluded by environment
    box3d = {'x': 3.0, 'y': 0.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_test_camera()
    
    # Create depth map with partial occlusion
    depth_img = np.full((480, 640), np.inf, dtype=np.float32)
    depth_img[200:280, 280:360] = 4.0  # Background
    depth_img[220:240, 300:320] = 2.0  # Partial occluder
    
    vis, stats = compute_visibility(
        box3d=box3d, cam=cam, depth_img=depth_img, cfg=config,
        method_arg='sphere', samples=1000, enable_diagnostics=True,
        debug_dir=None, obj_id='env_occluded', cam_name='test_cam'
    )
    
    # Verify conservative validation was used and improved visibility
    assert stats['conservative_enabled'] == True
    assert stats['in_loose_fov'] == True
    assert stats.get('conservative_vis') is not None
    assert stats['fusion_method'] == 'max'
    assert vis >= stats['sphere_vis']  # Should be >= sphere projection
    
    print(f"✓ Environmental occlusion test: vis={vis:.3f}, improvement={stats.get('vis_improvement', 0):.3f}")


def test_requirement_2_1():
    """Test Requirement 2.1: Sphere projection fallback with calibration errors."""
    print("Testing Requirement 2.1: Sphere projection fallback...")
    
    with open('configs/default.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Enable conservative validation
    config['conservative_validation']['enabled'] = True
    
    # Test with object that might have calibration issues
    box3d = {'x': 5.0, 'y': 2.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.5}
    cam = create_test_camera()
    depth_img = np.full((480, 640), np.inf, dtype=np.float32)
    
    vis, stats = compute_visibility(
        box3d=box3d, cam=cam, depth_img=depth_img, cfg=config,
        method_arg='sphere', samples=1000, enable_diagnostics=True,
        debug_dir=None, obj_id='calib_error', cam_name='test_cam'
    )
    
    # Verify sphere projection is always computed as baseline
    assert 'sphere_vis' in stats
    assert isinstance(stats['sphere_vis'], float)
    assert 0.0 <= stats['sphere_vis'] <= 1.0
    
    print(f"✓ Sphere projection fallback: sphere_vis={stats['sphere_vis']:.3f}")


def test_requirement_2_4():
    """Test Requirement 2.4: Backward compatibility."""
    print("Testing Requirement 2.4: Backward compatibility...")
    
    # Test with minimal configuration (no conservative validation section)
    config = {
        'visibility': {
            'tau_base_m': 2.0,
            'tau_scale_per_m': 0.1,
            'sphere_radius_m': 0.15,
            'occlusion_fraction_thr': 0.2,
            'min_neighbors': 3,
            'treat_no_depth_as_visible': True,
            'max_window_px': 15
        }
    }
    
    box3d = {'x': 2.0, 'y': 0.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_test_camera()
    depth_img = np.full((480, 640), 5.0, dtype=np.float32)
    
    vis, stats = compute_visibility(
        box3d=box3d, cam=cam, depth_img=depth_img, cfg=config,
        method_arg='sphere', samples=1000, enable_diagnostics=True,
        debug_dir=None, obj_id='backward_compat', cam_name='test_cam'
    )
    
    # Verify backward compatibility
    assert stats['conservative_enabled'] == False
    assert stats['conservative_vis'] is None
    assert stats['fusion_method'] == 'sphere_only'
    assert isinstance(vis, float)
    
    print(f"✓ Backward compatibility: conservative_enabled={stats['conservative_enabled']}")


def test_requirement_5_2():
    """Test Requirement 5.2: Diagnostic statistics."""
    print("Testing Requirement 5.2: Diagnostic statistics...")
    
    with open('configs/default.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    config['conservative_validation']['enabled'] = True
    
    box3d = {'x': 2.0, 'y': 0.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_test_camera()
    depth_img = np.full((480, 640), 3.0, dtype=np.float32)
    
    vis, stats = compute_visibility(
        box3d=box3d, cam=cam, depth_img=depth_img, cfg=config,
        method_arg='sphere', samples=1000, enable_diagnostics=True,
        debug_dir=None, obj_id='diagnostics_test', cam_name='test_cam'
    )
    
    # Verify all required diagnostic statistics are present
    required_stats = [
        'sphere_vis', 'conservative_enabled', 'fusion_method',
        'in_loose_fov', 'vis_improvement', 'cam_name', 'obj_id', 'method_used'
    ]
    
    for stat in required_stats:
        assert stat in stats, f"Missing diagnostic statistic: {stat}"
    
    print(f"✓ Diagnostic statistics complete: {len(required_stats)} fields present")
    print(f"  - sphere_vis: {stats['sphere_vis']:.3f}")
    print(f"  - conservative_vis: {stats.get('conservative_vis', 'N/A')}")
    print(f"  - fusion_method: {stats['fusion_method']}")
    print(f"  - vis_improvement: {stats['vis_improvement']:.3f}")


def test_max_function_logic():
    """Test that max function logic works correctly."""
    print("Testing max function combination logic...")
    
    with open('configs/default.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    config['conservative_validation']['enabled'] = True
    config['conservative_validation']['min_depth_points'] = 1
    
    box3d = {'x': 2.0, 'y': 0.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_test_camera()
    
    # Create depth map that should give good conservative visibility
    depth_img = np.full((480, 640), np.inf, dtype=np.float32)
    depth_img[200:280, 280:360] = 5.0  # Far background, no occlusion
    
    vis, stats = compute_visibility(
        box3d=box3d, cam=cam, depth_img=depth_img, cfg=config,
        method_arg='sphere', samples=1000, enable_diagnostics=True,
        debug_dir=None, obj_id='max_test', cam_name='test_cam'
    )
    
    # Verify max logic
    if stats.get('conservative_vis') is not None:
        expected_max = max(stats['sphere_vis'], stats['conservative_vis'])
        assert abs(vis - expected_max) < 1e-6, f"Max logic failed: expected {expected_max}, got {vis}"
        print(f"✓ Max function verified: max({stats['sphere_vis']:.3f}, {stats['conservative_vis']:.3f}) = {vis:.3f}")
    else:
        print(f"✓ Conservative validation not computed, using sphere only: {vis:.3f}")


if __name__ == '__main__':
    print("=== Task 2 Verification: Enhanced Visibility Dispatcher Integration ===\n")
    
    test_requirement_1_1_1_2()
    test_requirement_2_1()
    test_requirement_2_4()
    test_requirement_5_2()
    test_max_function_logic()
    
    print("\n✅ All Task 2 requirements verified successfully!")
    print("✅ Enhanced visibility dispatcher integration is complete!")