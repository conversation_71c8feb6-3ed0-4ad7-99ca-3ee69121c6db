"""
Integration tests for conservative validation with dispatcher and fusion modules.

Tests cover integration between conservative validation, dispatcher, and fusion
with various camera configurations and validation scenarios.
"""

import unittest
import numpy as np
import logging
from unittest.mock import Mock, patch, MagicMock

# Import modules under test
from robobus_vis.visibility.dispatcher import compute_visibility
from robobus_vis.visibility.fusion import (
    fuse_camera_visibility,
    fuse_camera_visibility_conservative,
    get_primary_visibility,
    to_occlusion_level
)
from robobus_vis.calib.camera_model import Camera

# Set up logging for tests
logging.basicConfig(level=logging.DEBUG)


class TestDispatcherIntegration(unittest.TestCase):
    """Integration tests for dispatcher with conservative validation."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create mock camera
        self.mock_camera = Mock()
        self.mock_camera.name = 'test_camera'
        self.mock_camera.T_base_cam = np.eye(4)
        self.mock_camera.project = Mock()
        
        # Standard test configuration with conservative validation enabled
        self.config_enabled = {
            'conservative_validation': {
                'enabled': True,
                'azimuth_tolerance_deg': 10.0,
                'min_depth_points': 5,
                'tau_multiplier': 1.5
            },
            'visibility': {
                'tau_base_m': 2.0,
                'tau_scale_per_m': 0.1,
                'sphere_radius_m': 0.15,
                'occlusion_fraction_thr': 0.2,
                'treat_no_depth_as_visible': True,
                'min_neighbors': 3,
                'max_window_px': 15
            }
        }
        
        # Configuration with conservative validation disabled
        self.config_disabled = {
            'conservative_validation': {
                'enabled': False
            },
            'visibility': {
                'tau_base_m': 2.0,
                'tau_scale_per_m': 0.1,
                'sphere_radius_m': 0.15,
                'occlusion_fraction_thr': 0.2,
                'treat_no_depth_as_visible': True,
                'min_neighbors': 3,
                'max_window_px': 15
            }
        }
        
        # Test box3d object
        self.test_box3d = {
            'x': 0.0,
            'y': 0.0,
            'z': 10.0,
            'l': 4.0,
            'w': 2.0,
            'h': 1.5,
            'yaw': 0.0
        }
        
        # Test depth map
        self.test_depth_map = np.full((200, 200), 15.0)  # All depths further than object
        
    @patch('robobus_vis.visibility.dispatcher.visibility_via_sphere_projection')
    @patch('robobus_vis.visibility.dispatcher.is_in_camera_fov_loose')
    @patch('robobus_vis.visibility.dispatcher.conservative_depth_validation')
    def test_dispatcher_conservative_enabled_in_fov(self, mock_depth_val, mock_fov_check, mock_sphere):
        """Test dispatcher with conservative validation enabled and object in FOV."""
        # Mock sphere projection
        mock_sphere.return_value = (0.6, {'samples': 100, 'hits': 60})
        
        # Mock FOV check - object is in loose FOV
        mock_fov_check.return_value = True
        
        # Mock conservative depth validation
        mock_depth_val.return_value = 0.8
        
        # Call dispatcher
        visibility, stats = compute_visibility(
            self.test_box3d,
            self.mock_camera,
            self.test_depth_map,
            self.config_enabled,
            'sphere',
            100,
            True,
            None,
            'test_obj',
            'test_camera'
        )
        
        # Verify conservative validation was called
        mock_fov_check.assert_called_once()
        mock_depth_val.assert_called_once()
        
        # Should take maximum of sphere (0.6) and conservative (0.8) = 0.8
        self.assertEqual(visibility, 0.8)
        
        # Check statistics
        self.assertEqual(stats['sphere_vis'], 0.6)
        self.assertEqual(stats['conservative_vis'], 0.8)
        self.assertEqual(stats['fusion_method'], 'max')
        self.assertAlmostEqual(stats['vis_improvement'], 0.2, places=5)
        self.assertTrue(stats['conservative_enabled'])
        self.assertTrue(stats['in_loose_fov'])
        
    @patch('robobus_vis.visibility.dispatcher.visibility_via_sphere_projection')
    @patch('robobus_vis.visibility.dispatcher.is_in_camera_fov_loose')
    def test_dispatcher_conservative_enabled_out_of_fov(self, mock_fov_check, mock_sphere):
        """Test dispatcher with conservative validation enabled but object out of FOV."""
        # Mock sphere projection
        mock_sphere.return_value = (0.6, {'samples': 100, 'hits': 60})
        
        # Mock FOV check - object is NOT in loose FOV
        mock_fov_check.return_value = False
        
        # Call dispatcher
        visibility, stats = compute_visibility(
            self.test_box3d,
            self.mock_camera,
            self.test_depth_map,
            self.config_enabled,
            'sphere',
            100,
            True,
            None,
            'test_obj',
            'test_camera'
        )
        
        # Verify FOV check was called but depth validation was not
        mock_fov_check.assert_called_once()
        
        # Should use sphere projection only
        self.assertEqual(visibility, 0.6)
        
        # Check statistics
        self.assertEqual(stats['sphere_vis'], 0.6)
        self.assertIsNone(stats['conservative_vis'])
        self.assertEqual(stats['fusion_method'], 'sphere_only')
        self.assertEqual(stats['vis_improvement'], 0.0)
        self.assertTrue(stats['conservative_enabled'])
        self.assertFalse(stats['in_loose_fov'])
        
    @patch('robobus_vis.visibility.dispatcher.visibility_via_sphere_projection')
    def test_dispatcher_conservative_disabled(self, mock_sphere):
        """Test dispatcher with conservative validation disabled."""
        # Mock sphere projection
        mock_sphere.return_value = (0.6, {'samples': 100, 'hits': 60})
        
        # Call dispatcher
        visibility, stats = compute_visibility(
            self.test_box3d,
            self.mock_camera,
            self.test_depth_map,
            self.config_disabled,
            'sphere',
            100,
            True,
            None,
            'test_obj',
            'test_camera'
        )
        
        # Should use sphere projection only
        self.assertEqual(visibility, 0.6)
        
        # Check statistics
        self.assertEqual(stats['sphere_vis'], 0.6)
        self.assertIsNone(stats['conservative_vis'])
        self.assertEqual(stats['fusion_method'], 'sphere_only')
        self.assertEqual(stats['vis_improvement'], 0.0)
        self.assertFalse(stats['conservative_enabled'])
        
    @patch('robobus_vis.visibility.dispatcher.visibility_via_sphere_projection')
    @patch('robobus_vis.visibility.dispatcher.conservative_depth_validation')
    def test_dispatcher_conservative_validation_error(self, mock_depth_val, mock_sphere):
        """Test dispatcher when conservative validation throws an error."""
        # Mock sphere projection
        mock_sphere.return_value = (0.6, {'samples': 100, 'hits': 60})
        
        # Mock conservative depth validation to raise an error
        mock_depth_val.side_effect = Exception("Test error")
        
        # Call dispatcher
        visibility, stats = compute_visibility(
            self.test_box3d,
            self.mock_camera,
            self.test_depth_map,
            self.config_enabled,
            'sphere',
            100,
            True,
            None,
            'test_obj',
            'test_camera'
        )
        
        # Should fall back to sphere projection
        self.assertEqual(visibility, 0.6)
        
        # Check error handling statistics
        self.assertEqual(stats['sphere_vis'], 0.6)
        self.assertIsNone(stats['conservative_vis'])
        self.assertEqual(stats['fusion_method'], 'sphere_fallback')
        self.assertEqual(stats['vis_improvement'], 0.0)
        self.assertIn('conservative_error', stats)
        
    @patch('robobus_vis.visibility.dispatcher.validate_conservative_config')
    @patch('robobus_vis.visibility.dispatcher.visibility_via_sphere_projection')
    def test_dispatcher_invalid_conservative_config(self, mock_sphere, mock_validate):
        """Test dispatcher with invalid conservative validation configuration."""
        # Mock sphere projection
        mock_sphere.return_value = (0.6, {'samples': 100, 'hits': 60})
        
        # Mock configuration validation to fail
        mock_validate.return_value = (False, "Invalid configuration")
        
        # Call dispatcher
        visibility, stats = compute_visibility(
            self.test_box3d,
            self.mock_camera,
            self.test_depth_map,
            self.config_enabled,
            'sphere',
            100,
            True,
            None,
            'test_obj',
            'test_camera'
        )
        
        # Should fall back to sphere projection only
        self.assertEqual(visibility, 0.6)
        
        # Conservative validation should be disabled due to invalid config
        self.assertFalse(stats['conservative_enabled'])
        
    def test_dispatcher_parameter_warnings(self):
        """Test dispatcher parameter validation warnings."""
        # Configuration with parameters that should trigger warnings
        config_with_warnings = {
            'conservative_validation': {'enabled': False},
            'visibility': {
                'tau_base_m': 0.7,  # Should trigger warning (> 0.6)
                'sphere_radius_m': 0.3,  # Should trigger warning (> 0.25)
                'tau_scale_per_m': 0.1,
                'occlusion_fraction_thr': 0.2,
                'treat_no_depth_as_visible': True,
                'min_neighbors': 3,
                'max_window_px': 15
            }
        }
        
        # Capture log output
        import logging
        import io
        
        log_capture_string = io.StringIO()
        ch = logging.StreamHandler(log_capture_string)
        ch.setLevel(logging.WARNING)
        
        dispatcher_logger = logging.getLogger('robobus_vis.visibility.dispatcher')
        dispatcher_logger.addHandler(ch)
        dispatcher_logger.setLevel(logging.WARNING)
        
        try:
            with patch('robobus_vis.visibility.dispatcher.visibility_via_sphere_projection') as mock_sphere:
                mock_sphere.return_value = (0.6, {'samples': 100, 'hits': 60})
                
                visibility, stats = compute_visibility(
                    self.test_box3d,
                    self.mock_camera,
                    self.test_depth_map,
                    config_with_warnings,
                    'sphere',
                    100,
                    True,
                    None,
                    'test_obj',
                    'test_camera'
                )
                
                # Check that warnings were logged
                log_contents = log_capture_string.getvalue()
                self.assertIn("tau_base_m=0.7 exceeds recommended maximum", log_contents)
                self.assertIn("sphere_radius_m=0.3 exceeds recommended maximum", log_contents)
                
        finally:
            dispatcher_logger.removeHandler(ch)


class TestFusionIntegration(unittest.TestCase):
    """Integration tests for fusion with different camera configurations."""
    
    def test_fusion_conservative_single_camera(self):
        """Test conservative fusion with single camera."""
        per_cam = {'front_camera': 0.7}
        
        vis_avg, vis_max, visible_views, fusion_stats = fuse_camera_visibility(
            per_cam, conservative_fusion=True
        )
        
        self.assertEqual(vis_avg, 0.7)
        self.assertEqual(vis_max, 0.7)
        self.assertEqual(visible_views, ['front_camera'])
        self.assertEqual(fusion_stats['fusion_method'], 'conservative_max')
        self.assertEqual(fusion_stats['primary_visibility'], 0.7)
        self.assertEqual(fusion_stats['num_cameras_visible'], 1)
        
    def test_fusion_conservative_multiple_cameras(self):
        """Test conservative fusion with multiple cameras."""
        per_cam = {
            'front_camera': 0.3,
            'left_camera': 0.8,
            'right_camera': 0.5,
            'back_camera': None  # No FOV
        }
        
        vis_avg, vis_max, visible_views, fusion_stats = fuse_camera_visibility(
            per_cam, conservative_fusion=True
        )
        
        self.assertAlmostEqual(vis_avg, (0.3 + 0.8 + 0.5) / 3, places=3)
        self.assertEqual(vis_max, 0.8)
        self.assertEqual(set(visible_views), {'front_camera', 'left_camera', 'right_camera'})
        self.assertEqual(fusion_stats['fusion_method'], 'conservative_max')
        self.assertEqual(fusion_stats['primary_visibility'], 0.8)  # Should use vis_max
        self.assertEqual(fusion_stats['num_cameras_visible'], 3)
        
    def test_fusion_traditional_vs_conservative(self):
        """Test difference between traditional and conservative fusion."""
        per_cam = {
            'front_camera': 0.2,
            'left_camera': 0.9,  # High visibility in one camera
            'right_camera': 0.1
        }
        
        # Traditional fusion
        vis_avg_trad, vis_max_trad, visible_views_trad, stats_trad = fuse_camera_visibility(
            per_cam, conservative_fusion=False
        )
        
        # Conservative fusion
        vis_avg_cons, vis_max_cons, visible_views_cons, stats_cons = fuse_camera_visibility(
            per_cam, conservative_fusion=True
        )
        
        # Both should calculate same avg and max
        self.assertEqual(vis_avg_trad, vis_avg_cons)
        self.assertEqual(vis_max_trad, vis_max_cons)
        
        # But primary visibility should differ
        self.assertEqual(stats_trad['primary_visibility'], vis_avg_trad)  # Traditional uses avg
        self.assertEqual(stats_cons['primary_visibility'], vis_max_cons)  # Conservative uses max
        
        # Conservative should prioritize max visibility
        self.assertGreater(stats_cons['primary_visibility'], stats_trad['primary_visibility'])
        
    def test_fusion_conservative_wrapper_function(self):
        """Test the conservative fusion wrapper function."""
        per_cam = {
            'front_camera': 0.4,
            'left_camera': 0.7,
            'right_camera': 0.2
        }
        
        vis_avg, vis_max, visible_views = fuse_camera_visibility_conservative(per_cam)
        
        self.assertAlmostEqual(vis_avg, (0.4 + 0.7 + 0.2) / 3, places=3)
        self.assertEqual(vis_max, 0.7)
        # Note: 0.2 >= 0.15, so right_camera should also be visible
        self.assertEqual(set(visible_views), {'front_camera', 'left_camera', 'right_camera'})  # >= 0.15 threshold
        
    def test_fusion_no_valid_cameras(self):
        """Test fusion with no valid camera data."""
        per_cam = {
            'front_camera': None,
            'left_camera': None,
            'right_camera': None
        }
        
        vis_avg, vis_max, visible_views, fusion_stats = fuse_camera_visibility(
            per_cam, conservative_fusion=True
        )
        
        self.assertEqual(vis_avg, 0.0)
        self.assertEqual(vis_max, 0.0)
        self.assertEqual(visible_views, [])
        self.assertEqual(fusion_stats['fusion_method'], 'no_data')
        self.assertEqual(fusion_stats['num_cameras_valid'], 0)
        
    def test_fusion_mixed_valid_invalid_cameras(self):
        """Test fusion with mix of valid and invalid camera data."""
        per_cam = {
            'front_camera': 0.6,
            'left_camera': None,
            'right_camera': 0.3,
            'back_camera': None
        }
        
        vis_avg, vis_max, visible_views, fusion_stats = fuse_camera_visibility(
            per_cam, include_noFOV_as_zero=True, conservative_fusion=True
        )
        
        # Should include None values as 0.0 when include_noFOV_as_zero=True
        expected_avg = (0.6 + 0.0 + 0.3 + 0.0) / 4
        self.assertAlmostEqual(vis_avg, expected_avg, places=3)
        self.assertEqual(vis_max, 0.6)
        self.assertEqual(set(visible_views), {'front_camera', 'right_camera'})
        self.assertEqual(fusion_stats['num_cameras_valid'], 4)  # All cameras counted
        self.assertEqual(fusion_stats['num_cameras_visible'], 2)  # Only >= 0.15
        
    def test_get_primary_visibility_function(self):
        """Test the get_primary_visibility utility function."""
        vis_avg = 0.4
        vis_max = 0.8
        
        # Conservative mode should return vis_max
        primary_conservative = get_primary_visibility(vis_avg, vis_max, conservative_fusion=True)
        self.assertEqual(primary_conservative, 0.8)
        
        # Traditional mode should return vis_avg
        primary_traditional = get_primary_visibility(vis_avg, vis_max, conservative_fusion=False)
        self.assertEqual(primary_traditional, 0.4)
        
    def test_occlusion_level_with_conservative_fusion(self):
        """Test occlusion level calculation with conservative fusion."""
        # Test various visibility scores
        test_cases = [
            (0.12, 1),  # >= 0.10 -> level 1
            (0.07, 2),  # >= 0.05 -> level 2
            (0.02, 3),  # >= 0.01 -> level 3
            (0.005, 4)  # < 0.01 -> level 4
        ]
        
        for visibility, expected_level in test_cases:
            level = to_occlusion_level(visibility, use_conservative=True)
            self.assertEqual(level, expected_level, 
                           f"Visibility {visibility} should map to level {expected_level}, got {level}")


class TestMultiCameraScenarios(unittest.TestCase):
    """Integration tests for multi-camera scenarios with conservative validation."""
    
    def setUp(self):
        """Set up multi-camera test scenario."""
        self.cameras = {
            'front': self._create_test_camera('front', position=(0, 0, 0), yaw_deg=0),
            'left': self._create_test_camera('left', position=(0, 0, 0), yaw_deg=90),
            'right': self._create_test_camera('right', position=(0, 0, 0), yaw_deg=-90),
            'back': self._create_test_camera('back', position=(0, 0, 0), yaw_deg=180)
        }
        
        self.config = {
            'conservative_validation': {
                'enabled': True,
                'azimuth_tolerance_deg': 15.0,
                'min_depth_points': 3,
                'tau_multiplier': 1.2
            },
            'visibility': {
                'tau_base_m': 2.0,
                'tau_scale_per_m': 0.1,
                'sphere_radius_m': 0.15,
                'occlusion_fraction_thr': 0.2,
                'treat_no_depth_as_visible': True,
                'min_neighbors': 3,
                'max_window_px': 15
            }
        }
        
    def _create_test_camera(self, name, position=(0, 0, 0), yaw_deg=0):
        """Create a test camera with specified parameters."""
        K = np.array([[800.0, 0, 640.0], [0, 800.0, 360.0], [0, 0, 1.0]], dtype=float)
        
        # Create transformation matrix
        yaw_rad = np.deg2rad(yaw_deg)
        c, s = np.cos(yaw_rad), np.sin(yaw_rad)
        R_base_cam = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]], dtype=float)
        t_base_cam = np.array(position, dtype=float)
        
        T_base_cam = np.eye(4)
        T_base_cam[:3, :3] = R_base_cam.T
        T_base_cam[:3, 3] = -R_base_cam.T @ t_base_cam
        
        camera = Camera(name=name, K=K, dist=None, width=1280, height=720, T_base_cam=T_base_cam)
        camera.project = Mock(return_value=np.array([[640, 360, 10.0]]))  # Mock projection
        return camera
        
    @patch('robobus_vis.visibility.dispatcher.visibility_via_sphere_projection')
    @patch('robobus_vis.visibility.dispatcher.is_in_camera_fov_loose')
    @patch('robobus_vis.visibility.dispatcher.conservative_depth_validation')
    def test_multi_camera_conservative_fusion_scenario(self, mock_depth_val, mock_fov_check, mock_sphere):
        """Test complete multi-camera scenario with conservative validation and fusion."""
        # Object in front-right quadrant - should be visible to front and right cameras
        test_box3d = {
            'x': 5.0,   # Right side
            'y': 0.0,   # Center
            'z': 10.0,  # Forward
            'l': 4.0, 'w': 2.0, 'h': 1.5, 'yaw': 0.0
        }
        
        # Mock different visibility scores for different cameras
        sphere_scores = {'front': 0.4, 'left': 0.1, 'right': 0.7, 'back': 0.0}
        conservative_scores = {'front': 0.6, 'left': 0.0, 'right': 0.9, 'back': 0.0}
        fov_results = {'front': True, 'left': False, 'right': True, 'back': False}
        
        def sphere_side_effect(*args, **kwargs):
            cam_name = args[1].name if hasattr(args[1], 'name') else 'front'
            return sphere_scores.get(cam_name, 0.0), {'samples': 100, 'hits': 50}
            
        def fov_side_effect(center, camera, tolerance_deg):
            cam_name = camera.name if hasattr(camera, 'name') else 'front'
            return fov_results.get(cam_name, False)
            
        def depth_side_effect(center, dims, yaw, camera, depth_map, config):
            cam_name = camera.name if hasattr(camera, 'name') else 'front'
            return conservative_scores.get(cam_name, 0.0)
        
        mock_sphere.side_effect = sphere_side_effect
        mock_fov_check.side_effect = fov_side_effect
        mock_depth_val.side_effect = depth_side_effect
        
        # Calculate visibility for each camera
        per_cam_visibility = {}
        depth_map = np.full((720, 1280), 15.0)
        
        for cam_name, camera in self.cameras.items():
            visibility, stats = compute_visibility(
                test_box3d, camera, depth_map, self.config,
                'sphere', 100, True, None, 'test_obj', cam_name
            )
            per_cam_visibility[cam_name] = visibility
            
        # Expected results:
        # front: max(0.4, 0.6) = 0.6 (in FOV, conservative helps)
        # left: 0.1 (not in FOV, sphere only)
        # right: max(0.7, 0.9) = 0.9 (in FOV, conservative helps)
        # back: 0.0 (not in FOV, sphere only)
        
        expected_visibility = {'front': 0.6, 'left': 0.1, 'right': 0.9, 'back': 0.0}
        
        for cam_name, expected in expected_visibility.items():
            self.assertEqual(per_cam_visibility[cam_name], expected,
                           f"Camera {cam_name} visibility should be {expected}, got {per_cam_visibility[cam_name]}")
        
        # Test fusion
        vis_avg, vis_max, visible_views, fusion_stats = fuse_camera_visibility(
            per_cam_visibility, conservative_fusion=True
        )
        
        # Check fusion results
        expected_avg = (0.6 + 0.1 + 0.9 + 0.0) / 4
        self.assertAlmostEqual(vis_avg, expected_avg, places=3)
        self.assertEqual(vis_max, 0.9)  # Maximum from right camera
        
        # Visible views should include cameras with visibility >= 0.15
        expected_visible = {'front', 'right'}  # 0.6 and 0.9 >= 0.15
        self.assertEqual(set(visible_views), expected_visible)
        
        # Conservative fusion should use vis_max as primary
        self.assertEqual(fusion_stats['primary_visibility'], 0.9)
        self.assertEqual(fusion_stats['fusion_method'], 'conservative_max')
        
        # Test occlusion level calculation
        occlusion_level = to_occlusion_level(fusion_stats['primary_visibility'], use_conservative=True)
        self.assertEqual(occlusion_level, 1)  # 0.9 >= 0.10 -> level 1 (visible)
        
    def test_calibration_error_robustness(self):
        """Test robustness to calibration errors through conservative validation."""
        # Simulate calibration error by using different camera configurations
        # This test verifies that conservative validation provides robustness
        
        # Test with sphere projection giving low score due to calibration error
        # but conservative validation providing higher score
        
        with patch('robobus_vis.visibility.dispatcher.visibility_via_sphere_projection') as mock_sphere, \
             patch('robobus_vis.visibility.dispatcher.is_in_camera_fov_loose') as mock_fov, \
             patch('robobus_vis.visibility.dispatcher.conservative_depth_validation') as mock_depth:
            
            # Sphere projection affected by calibration error (low score)
            mock_sphere.return_value = (0.2, {'samples': 100, 'hits': 20})
            
            # Conservative validation less affected (higher score)
            mock_fov.return_value = True
            mock_depth.return_value = 0.7
            
            test_box3d = {'x': 0.0, 'y': 0.0, 'z': 10.0, 'l': 4.0, 'w': 2.0, 'h': 1.5, 'yaw': 0.0}
            depth_map = np.full((720, 1280), 15.0)
            
            visibility, stats = compute_visibility(
                test_box3d, self.cameras['front'], depth_map, self.config,
                'sphere', 100, True, None, 'test_obj', 'front'
            )
            
            # Should take maximum (conservative validation helps)
            self.assertEqual(visibility, 0.7)
            self.assertAlmostEqual(stats['vis_improvement'], 0.5, places=5)  # 0.7 - 0.2
            self.assertEqual(stats['fusion_method'], 'max')


if __name__ == '__main__':
    unittest.main()