#!/usr/bin/env python3
"""
Test dispatcher with object in FOV to see conservative validation in action.
"""

import sys
import numpy as np
import yaml
from unittest.mock import Mock

# Add project root to path
sys.path.append('.')

from robobus_vis.visibility.dispatcher import compute_visibility


def create_mock_camera_close():
    """Create a mock camera with object close to camera center."""
    cam = Mock()
    # Identity transformation - camera at origin looking forward
    cam.T_base_cam = np.eye(4)
    
    # Mock projection that puts object in center of image
    def mock_project(points):
        # Return points projected to center of image with reasonable depth
        n_points = len(points) if hasattr(points, '__len__') else 1
        if n_points == 1:
            return np.array([[320, 240, 2.0]])  # Center of 640x480 image
        else:
            # Multiple points around center
            result = []
            for i in range(n_points):
                u = 320 + np.random.uniform(-50, 50)  # Around center
                v = 240 + np.random.uniform(-50, 50)  # Around center
                z = 2.0 + np.random.uniform(-0.5, 0.5)  # Around 2m depth
                result.append([u, v, z])
            return np.array(result)
    
    cam.project = mock_project
    return cam


def test_conservative_validation_in_fov():
    """Test conservative validation when object is in FOV."""
    print("Testing conservative validation with object in FOV...")
    
    # Load configuration
    with open('configs/default.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Enable conservative validation
    config['conservative_validation']['enabled'] = True
    config['conservative_validation']['min_depth_points'] = 3  # Lower threshold for testing
    
    # Create test data - object close to camera center
    box3d = {'x': 2.0, 'y': 0.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_mock_camera_close()
    
    # Create depth map with some valid depth values
    depth_img = np.full((480, 640), np.inf, dtype=np.float32)
    # Add some depth values around the center where object projects
    depth_img[220:260, 300:340] = 2.5  # Slightly further than object
    depth_img[240:250, 315:325] = 1.8  # Closer than object (occluder)
    
    # Call dispatcher
    vis, stats = compute_visibility(
        box3d=box3d,
        cam=cam,
        depth_img=depth_img,
        cfg=config,
        method_arg='sphere',
        samples=1000,
        enable_diagnostics=True,
        debug_dir=None,
        obj_id='test_obj_in_fov',
        cam_name='test_cam'
    )
    
    # Print detailed results
    print(f"✓ Visibility: {vis:.3f}")
    print(f"✓ Conservative enabled: {stats['conservative_enabled']}")
    print(f"✓ Sphere vis: {stats['sphere_vis']:.3f}")
    print(f"✓ Conservative vis: {stats.get('conservative_vis', 'N/A')}")
    print(f"✓ Fusion method: {stats['fusion_method']}")
    print(f"✓ In loose FOV: {stats['in_loose_fov']}")
    print(f"✓ Visibility improvement: {stats.get('vis_improvement', 0.0):.3f}")
    
    # Verify conservative validation was attempted
    assert stats['conservative_enabled'] == True
    assert stats['in_loose_fov'] == True  # Should be in FOV
    
    if stats.get('conservative_vis') is not None:
        print("✓ Conservative validation successfully computed")
        assert stats['fusion_method'] == 'max'
    else:
        print("✓ Conservative validation attempted but returned None (acceptable)")


def test_max_fusion_logic():
    """Test that max fusion logic works correctly."""
    print("\nTesting max fusion logic...")
    
    # Load configuration
    with open('configs/default.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Enable conservative validation
    config['conservative_validation']['enabled'] = True
    config['conservative_validation']['min_depth_points'] = 1  # Very low threshold
    
    # Adjust visibility parameters to get non-zero sphere visibility
    config['visibility']['treat_no_depth_as_visible'] = True
    config['visibility']['occlusion_fraction_thr'] = 0.8  # High threshold
    
    # Create test data
    box3d = {'x': 2.0, 'y': 0.0, 'z': 1.5, 'l': 4.0, 'w': 2.0, 'h': 1.8, 'yaw': 0.0}
    cam = create_mock_camera_close()
    
    # Create depth map that should give some visibility
    depth_img = np.full((480, 640), np.inf, dtype=np.float32)
    # Add depth values that are further than object (no occlusion)
    depth_img[200:280, 280:360] = 5.0  # Much further than object at 2m
    
    # Call dispatcher
    vis, stats = compute_visibility(
        box3d=box3d,
        cam=cam,
        depth_img=depth_img,
        cfg=config,
        method_arg='sphere',
        samples=1000,
        enable_diagnostics=True,
        debug_dir=None,
        obj_id='test_max_fusion',
        cam_name='test_cam'
    )
    
    print(f"✓ Final visibility: {vis:.3f}")
    print(f"✓ Sphere vis: {stats['sphere_vis']:.3f}")
    print(f"✓ Conservative vis: {stats.get('conservative_vis', 'N/A')}")
    print(f"✓ Fusion method: {stats['fusion_method']}")
    
    # Verify max fusion was used if both methods computed values
    if stats.get('conservative_vis') is not None:
        expected_max = max(stats['sphere_vis'], stats['conservative_vis'])
        assert abs(vis - expected_max) < 1e-6, f"Expected {expected_max}, got {vis}"
        print("✓ Max fusion logic verified")


if __name__ == '__main__':
    test_conservative_validation_in_fov()
    test_max_fusion_logic()
    print("\n✅ All FOV scenario tests passed!")