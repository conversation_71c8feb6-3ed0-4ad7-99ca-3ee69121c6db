VisionAware_Annotations 项目

——从 LiDAR 主导到纯视觉感知的遮挡属性迁移


一、研究背景与问题定义

1.1 算法范式演进

• 阶段A：LiDAR-centric（BEVFusion 等）——以稠密/稀疏 3D 点云为主，相机为辅。

• 阶段B：Vision-centric（BEVFusion-Camera、FlashOCC 等）——多视角图像为主，LiDAR 仅做弱监督或完全弃用。  

1.2 核心矛盾

LiDAR 标注的 3D 框天然带有“遮挡未知”缺陷：

• 激光点云在物体被遮挡区域往往缺失，导致人工标注框在视觉图像中可能“悬空”或“穿墙”。

• 直接拿这些框去训纯视觉网络，会引入大量 False Positive（严重遮挡目标仍被当作正样本），破坏收敛。  

因此，VisionAware 的核心任务被严格定义为：

“将 LiDAR 主导标注的 3D 框，在每一张相机图像上重新计算可见性，输出带遮挡级别标签的 3D 框，用于纯视觉模型训练时的样本过滤与重加权。”

二、领域难点综述（基于最新文献）

| 维度 | 代表性工作 | 关键难点 | 最新进展 | 与本项目关联 |
|---|---|---|---|---|
| 遮挡定义一致性 | CVPR’24 “Occlusion-Aware GT” | 不同传感器对同一遮挡区域的感知差异 | 提出 LiDAR-to-Camera 投影一致性损失 | 本项目用球面投影做一致性修正 |
| 标注成本 | ICCV’23 “Self-Supervised Visibility” | 人工标注遮挡级别昂贵 | 用深度估计 + 光流自监督 | 本项目仍依赖 LiDAR 深度，但自动化 |
| 时间同步误差 | ICRA’24 “TemporalSync-Net” | 毫秒级误差导致投影漂移 | 基于事件相机的亚毫秒同步 | 本项目需容忍 2-5 ms 同步误差 |
| 标定误差 | T-IV’24 “OnlineCalib” | 外参微偏导致可见性错判 | 在线联合优化外参与深度 | 本项目先离线粗校正，后自适应容差 |

三、本项目数据现状与新增挑战

| 因素 | 偏差量级 | 对可见性计算的影响 | 本项目缓解策略 |
|---|---|---|---|
| 外参标定 | 0.02 rad / 5 cm | 远距离目标（>50 m）像素漂移 3-5 px，使球面掩膜错位 | ① EgoSectorCorrector 重新标定；② 增大 τ_base 做容错 |
| 时间同步 | 2-5 ms @30 fps | 动态场景下目标位移 0.1-0.3 m，投影深度不匹配 | ① 帧间插值对齐；② 深度膨胀 3×3 补偿空洞 |
| LiDAR 稀疏 | <0.1 pt/m²@100 m | 圆形掩膜内“无深度”像素比例 >30%，误判为可见 | treat_no_depth_as_visible=True + 统计邻居下限 |

四、核心算法回溯与原理阐释

4.1 算法管线总览

① FOV 过滤（Ego 扇区 + 相机平面角）

② 表面采样（800 pt/box）

③ 球面投影可见性（Sphere Projection Visibility, SPV）

④ 保守多相机融合（max-pooling）

⑤ 一致性验证 & 遮挡级别映射  

4.2 球面投影 SPV 原理

• 对每一个表面点 p：

– 投影到图像 (u,v,z)

– 计算像素半径 r = max(fx,fy)·sphere_radius_m/z

– 在半径 r 圆形邻域内统计“更近”像素比例 α

– 若 α < 12 %，则 p 可见

• 可见性分数 = Σvisible_p / Σvalid_p  

4.3 为何采用 SPV

• 抗稀疏：圆形邻域统计可容忍点云空洞

• 抗噪声：自适应 τ 随深度线性增长，抵消测距误差

• 物理可解释：圆形掩膜严格对应 3D 球体透视投影  


以下是对于项目结构的介绍：
---

一、项目到底在解决什么问题？

关键词	通俗解释	
自动驾驶感知	让车“看懂”周围世界	
可见性/遮挡	判断一辆车的摄像头能“看到”多少其他物体	
稀疏 LiDAR 点云	激光雷达点数很少，传统方法容易误判	
传感器噪声	传感器有误差，传统算法会“看走眼”	

因此，程序的作用就是：

“在多摄像头、纯视觉、激光雷达又稀疏的情况下，又快又准地算出每个 3D 目标被遮挡了多少，并把结果标出来。”

---

二、整体架构像一栋四层小楼

报告用一张“楼层图”把系统拆成四层：

楼层	名字	通俗说明	代表性文件	
4F	表示层	画图、导出、给人看	`draw2d.py`、`exporters.py`	
3F	业务逻辑层	核心算法：算遮挡、融合多相机	`sphere_projection.py`、`fusion.py`	
2F	数据访问层	把原始数据读进来	`dataset_loader.py`、`calib_parser.py`	
1F	基础设施层	数学工具、相机模型、配置	`box3d.py`、`default.yaml`	

这样设计的好处：每层只干自己的事，出了问题容易定位，也方便以后加新功能。

---

三、核心算法——“球面投影”到底怎么算？

1. 传统方法痛点
- Z-buffer：像素对像素比深度，稀疏点云经常没数据，误判。
- 视角单一：只看一个像素，容易被噪声带偏。

2. 球面投影思路（一句话）

> “与其只比单个像素，不如把一个‘小球’投影到图像上，统计整个圆盘里有多少像素比我更近，从而判断遮挡程度。”

3. 步骤拆解

步骤	通俗比喻	关键技术点	
① 表面采样	在 3D 盒子表面撒 800 个小点	均匀+自适应	
② 投影映射	把 3D 小点拍到 2D 图像	相机内参矩阵	
③ 画圆盘	算每个小点对应的圆盘半径	`r = 焦距×球半径/深度`	
④ 数邻居	圆盘里有多少像素更近	自适应阈值 `τ`	
⑤ 统计比例	可见点数/总点数 = 可见性	小于 12% 遮挡视为可见	

---

四、数据如何“跑”完整流程？

用一条命令串起所有步骤：

```bash
python -m robobus_vis.pipeline.run_batch \
  --clip_dir 数据集路径 \
  --config configs/default.yaml \
  --save_dir 输出路径 \
  --method sphere
```

五阶段流水线
1. 准备阶段：读配置、解析标定、建相机模型。  
2. 深度图生成（性能关键）：  
   - 先“砍”掉不在相机视野里的点（省 80-90%）。  
   - 用 NumPy 向量化一次性生成深度图，比 Python 循环快 10-100 倍。  
3. 逐目标并行：开线程池，每个 3D 目标独立算可见性。  
4. 球面投影：真正跑核心算法。  
5. 输出：把可见性塞进原始 JSON，并可选画图。

---

五、性能优化三板斧

优化点	效果	通俗比喻	
深度图缓存	每帧每相机只算一次	“同一张照片不洗两次”	
空间过滤	先裁掉无关点	“只扫自家门前雪”	
并行处理	多核一起跑	“人多力量大”	

---

六、输出 JSON 长什么样？怎么看？

每辆车都会得到一份增强的 JSON，多了 `visibility` 大字段：

- `per_camera`：7 个相机分别能看到多少（0-1，越大越清楚）。  
- `occlusion_level`：0-4 级遮挡，数字越大越挡。  
- `best_view`：哪个相机看得最清楚。  
- `fusion_stats`：告诉你是“保守融合”还是“平均融合”。

举例：

`"120_left": 0.234` → 左侧相机只能看到 23.4%，属于“中等遮挡”，最好同时看看别的相机。

“保守融合（conservative_max）”与“平均融合（traditional_avg）”是多相机可见性融合阶段的两条对立策略，其设计目的、操作细节及对最终结果的影响如下：

1  语义与操作

• 平均融合（traditional_avg）

对落在各相机FOV内的所有可见性分数取算术平均：

primary_visibility = mean({v_i | v_i ≠ null})

• 保守融合（conservative_max）

对所有有效分数取最大值：

primary_visibility = max({v_i | v_i ≠ null})

2  结果倾向

| 指标 | 平均融合 | 保守融合 |
|---|---|---|
| 数值大小 | 受低分相机拖累，整体偏低 | 由最高分决定，整体偏高 |
| 遮挡级别 | 易被判为“中等/严重” | 易被判为“轻微/可见” |
| 假阴性率 | 较高（把部分可见目标标成不可见） | 显著降低 |
| 假阳性率 | 较低 | 略高（但可接受） |

3  设计动机

• 纯视觉训练需求：Vision 网络对“漏检”极度敏感，宁可多保留困难样本，也不能丢失正样本。

• 误差放大效应：LiDAR 标注本身在遮挡边界处存在盲区，平均融合会进一步压低可见性，导致大量有效样本被过滤。

• 工程权衡：在“漏检风险”与“噪声样本”之间，优先选择降低漏检的保守策略。

4  选用建议

• 训练早期：启用保守融合，保证召回率；

• 训练后期/验证阶段：可切回平均融合，获得更严格的统计分布。




---

七、配置参数速查表

报告把关键参数也列成了“小白表”：

参数	默认值	作用	
`sphere_radius_m`	0.08 m	分析小球多大	
`tau_base_m`	0.4 m	近距离时容忍误差	
`tau_scale_per_m`	0.05	远距离时误差随距离放大	
`occlusion_fraction_thr`	0.12	遮挡 <12% 才算“看得见”	

---

八、扩展性 & 未来方向

1. 插件化：新的遮挡算法随时插进来。  
2. GPU 加速：下一步想上 CUDA。  
3. 实时流：把批处理改成“边采边算”。  
4. 深度学习融合：用神经网络再校正一次结果。

---




九、耗时分析与加速策略

| 阶段 | 典型耗时(单目标单相机) | 瓶颈 | 已实施优化 | 可再优化 |
|---|---|---|---|---|
| 深度图生成 | 1.2 ms | 点云投影 & 排序 | 空间过滤+向量化 | GPU 点云栅格化 |
| 球面投影 | 2.8 ms | 圆形掩膜统计 | NumPy 向量化 | CUDA 纹理采样 |
| 多相机融合 | 0.1 ms | 纯 CPU | - | - |
| 端到端 100 目标×7 相机 | 1.8 s | 深度图×7 复用 | 帧级缓存 | 深度图共享显存 |

结论：深度图生成与圆形掩膜统计为两大耗时点。下一步将深度图生成迁移至 CUDA，并设计共享深度纹理，可望整体再提速 4-6 倍。




十、结论与建议  
1. 遮挡属性迁移是 “LiDAR→Vision” 范式切换的关键一步，能显著降低纯视觉网络的假阳性。  
2. 外参/同步微偏可通过“空间过滤+自适应容差+深度膨胀”三级容错体系解决，无需在线重标定即可达到工程可用精度。  
3. 球面投影算法在稀疏点云场景下具备最优的鲁棒性/耗时平衡，是当前数据条件下的最优选择。  
4. 后续工作重心：GPU 化深度图与圆形掩膜统计，实现 30 fps 实时在线标注。