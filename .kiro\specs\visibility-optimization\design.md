# Design Document

## Overview

The enhanced visibility system combines the Sphere Projection Visibility Algorithm (calibration-independent) with multi-camera conservative validation. This design addresses the core limitations of the current system: inability to handle environmental occlusions without 3D bounding boxes and false negatives caused by inaccurate calibration and sparse point clouds.

The solution prioritizes visibility unless there is clear evidence of occlusion, uses generous azimuth tolerances for FOV gating, handles sparse depth data conservatively, and fuses visibility scores across cameras using maximum values to minimize false negatives.

## Architecture

### High-Level Architecture

The system maintains the existing pipeline structure while adding conservative validation as an optional enhancement layer:

```
Input (3D Object + Camera + Depth Map)
    ↓
Sphere Projection Baseline Visibility
    ↓
Conservative Camera Validation (Optional)
    ↓
Per-Camera Visibility Score
    ↓
Multi-Camera Fusion (Max Strategy)
    ↓
Output (Augmented JSON with Visibility Scores)
```

### Design Principles

1. **Conservative Approach**: Prioritize visibility unless overwhelming evidence of occlusion exists
2. **Calibration Independence**: Maintain sphere projection as the robust baseline method
3. **Configurable Behavior**: Allow tuning of conservative parameters via YAML configuration
4. **Backward Compatibility**: Preserve existing JSON output formats and API contracts
5. **Performance Preservation**: Maintain parallel processing and caching strategies

## Components and Interfaces

### Component 1: Conservative Camera Validation Module

**File**: `robobus_vis/visibility/conservative_validation.py`

**Purpose**: Provides loose FOV gating and conservative depth map validation for individual cameras.

**Key Functions**:

```python
def is_in_camera_fov_loose(object_center: np.ndarray, 
                          camera: CameraModel, 
                          tolerance_deg: float = 10.0) -> bool:
    """
    Check if object is within camera FOV with generous tolerance.
    
    Args:
        object_center: 3D object center coordinates
        camera: Camera model with FOV parameters
        tolerance_deg: Additional degrees beyond nominal FOV
        
    Returns:
        True if object is within expanded FOV
    """
    
def conservative_depth_validation(box3d: Box3D,
                                camera: CameraModel,
                                depth_map: np.ndarray,
                                config: dict) -> float:
    """
    Perform conservative depth-based visibility validation.
    
    Args:
        box3d: 3D bounding box of the object
        camera: Camera model for projection
        depth_map: Dense depth map from point cloud
        config: Configuration parameters
        
    Returns:
        Visibility score between 0.0 and 1.0
    """
```

**Algorithm Details**:

1. **Loose FOV Gating**: Expand nominal camera FOV by configurable tolerance (default ±10°)
2. **Sparse Data Handling**: If valid depth points < threshold, assume visibility = 1.0
3. **Conservative Occlusion Counting**: Use increased tau tolerances (1.5x multiplier) for depth comparison
4. **Visibility Calculation**: `visibility = 1.0 - (occluded_points / total_points)`

### Component 2: Enhanced Visibility Dispatcher

**File**: `robobus_vis/visibility/dispatcher.py` (Modified)

**Purpose**: Orchestrates visibility computation by combining sphere projection with optional conservative validation.

**Key Modifications**:

```python
def compute_visibility(box3d: Box3D, 
                      cam: CameraModel, 
                      depth_img: np.ndarray, 
                      cfg: dict, 
                      **kwargs) -> Tuple[float, dict]:
    """
    Enhanced visibility computation with conservative validation option.
    
    Returns:
        Tuple of (visibility_score, statistics_dict)
    """
    # Baseline sphere projection visibility
    sphere_vis, stats = visibility_via_sphere_projection(box3d, cam, depth_img, cfg)
    
    # Optional conservative validation
    if cfg.get('conservative_validation', {}).get('enabled', False):
        camera_vis = conservative_camera_validation(box3d, cam, depth_img, cfg)
        # Take maximum to reduce false negatives
        final_vis = max(sphere_vis, camera_vis)
        stats['conservative_vis'] = camera_vis
        stats['fusion_method'] = 'max'
    else:
        final_vis = sphere_vis
        
    return final_vis, stats
```

### Component 3: Enhanced Fusion Module

**File**: `robobus_vis/visibility/fusion.py` (Modified)

**Purpose**: Aggregates per-camera visibility scores using maximum-based fusion to minimize false negatives.

**Key Modifications**:

```python
def fuse_camera_visibility(per_cam: Dict[str, float]) -> Tuple[float, float, List[str]]:
    """
    Fuse visibility scores across cameras with conservative strategy.
    
    Args:
        per_cam: Dictionary mapping camera names to visibility scores
        
    Returns:
        Tuple of (vis_avg, vis_max, visible_views)
    """
    vals = [v for v in per_cam.values() if v is not None]
    if not vals:
        return 0.0, 0.0, []
        
    vis_avg = sum(vals) / len(vals)
    vis_max = max(vals)  # Primary metric for conservative fusion
    
    # Determine visible views with conservative threshold
    visible_views = [cam for cam, v in per_cam.items() 
                    if v is not None and v >= 0.15]
    
    return vis_avg, vis_max, visible_views
```

## Data Models

### Configuration Schema Extensions

**File**: `configs/default.yaml` (Extended)

```yaml
# New conservative validation section
conservative_validation:
  enabled: true                    # Enable/disable conservative validation
  azimuth_tolerance_deg: 10.0     # FOV expansion tolerance in degrees
  min_depth_points: 5             # Minimum depth points to trust depth data
  tau_multiplier: 1.5             # Multiplier for depth tolerance parameters

# Enhanced FOV filter settings
fov_filter:
  soft_margin_deg: 10.0           # Increased default for loose gating
  
# Enhanced visibility settings  
visibility:
  treat_no_depth_as_visible: true # Assume visibility when depth is insufficient
  sphere_radius_m: 0.5            # Existing parameter
  occlusion_fraction_thr: 0.3     # Existing parameter
```

### Output Data Model Extensions

The JSON output format remains backward compatible with additional diagnostic fields:

```json
{
  "visibility": {
    "vis_avg": 0.75,
    "vis_max": 0.90,
    "visible_views": ["front_camera", "left_camera"],
    "per_camera": {
      "front_camera": {
        "visibility": 0.90,
        "sphere_vis": 0.85,
        "conservative_vis": 0.90,
        "fusion_method": "max"
      }
    },
    "occlusion_level": 1
  }
}
```

### occlusion_level
The current program will add a field called "occlusion_level" to the output results to indicate the degree of occlusion. "occlusion_level": 1 indicates fully visible; "occlusion_level": 2 indicates slightly occluded; "occlusion_level": 3 indicates heavily occluded; "occlusion_level": 4 indicates fully occluded. This field will be used in the subsequent training of pure vision perception models to filter out targets that are heavily or fully occluded, preventing the pure vision model from learning about invisible targets.

## Error Handling

### Sparse Depth Data

- **Detection**: Count valid depth points in object projection region
- **Threshold**: Configurable minimum (default: 5 points)
- **Response**: Assume visibility = 1.0 when below threshold
- **Logging**: Record sparse data events for monitoring

### Camera Calibration Errors

- **Fallback**: Always compute sphere projection as baseline
- **Validation**: Conservative validation provides additional robustness
- **Fusion**: Maximum-based fusion reduces impact of single-camera errors

### Configuration Errors

- **Safe Defaults**: Provide sensible defaults for all new parameters
- **Validation**: Validate configuration parameters at startup
- **Graceful Degradation**: Disable conservative validation if configuration is invalid

## Testing Strategy

### Unit Testing

1. **Conservative Validation Functions**
   - Test loose FOV gating with various tolerance values
   - Test depth validation with sparse and dense data
   - Test edge cases (empty depth maps, extreme tolerances)

2. **Integration Testing**
   - Test dispatcher with conservative validation enabled/disabled
   - Test fusion logic with various camera configurations
   - Test configuration loading and validation

### Performance Testing

1. **Benchmark Comparison**
   - Measure performance impact of conservative validation
   - Compare processing times with baseline system
   - Validate parallel processing still functions correctly

2. **Memory Usage**
   - Monitor memory consumption with additional validation
   - Ensure caching strategies remain effective

### Validation Testing

1. **False Negative Analysis**
   - Test on validation datasets with known ground truth
   - Measure false negative rate reduction
   - Compare visibility scores before and after enhancement

2. **Robustness Testing**
   - Test with various calibration error scenarios
   - Test with different point cloud densities
   - Test with environmental occlusion cases

## Implementation Considerations

### Performance Optimization

- Conservative validation only runs when enabled in configuration
- Reuse existing depth map caching infrastructure
- Maintain parallel object processing with ThreadPoolExecutor
- Add minimal overhead to existing pipeline

### Backward Compatibility

- All existing configuration parameters remain functional
- JSON output format maintains existing fields
- API contracts for visibility functions unchanged
- Conservative validation is opt-in via configuration

### Monitoring and Diagnostics

- Add logging for conservative validation decisions
- Include diagnostic fields in output JSON
- Maintain existing performance instrumentation
- Add configuration validation warnings