import argparse
from pathlib import Path
import json
import yaml
import numpy as np
import cv2
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)
from ..io.dataset_loader import ClipDataset
from ..io.calib_parser import parse_calibrated_sensor_pb_txt
# Removed PoseStream import - no longer needed for simplified pipeline
from ..calib.camera_model import Camera
from ..config.camera_correction import create_camera_corrector
# from ..calib.transforms import transform_points, inv_T  # unused
from ..geometry.depth_tools import pointcloud_to_depth, apply_depth_dilation
from ..geometry.box3d import sample_box_surface, sample_box_surface_adaptive, box_corners
from ..visibility.dispatcher import compute_visibility
# Simplified imports - only keep essential visibility functions
from ..visibility.fusion import (
    fuse_camera_visibility, to_occlusion_level,
    validate_occlusion_consistency, calculate_visibility_statistics
)
# Removed visibility_confidence import - not needed in simplified schema
# Removed sync_compensation import - data is pre-synchronized
from ..visibility.fov_filter import ego_sector_gate, cam_fov_gate_point, cam_fov_gate_corners
from ..vis.draw2d import draw_box_projections, overlay_visibility_score, make_mosaic
from ..vis.exporters import save_image

# End-to-end batch with projection visualization and sync fields


def load_pcd_xyz(pcd_path: Path) -> np.ndarray:
    """Load XYZ coordinates from PCD file, handling both plain text and PCD format."""
    try:
        # First try loading as plain text (for backward compatibility)
        pts = np.loadtxt(str(pcd_path), usecols=(0,1,2))
        if pts.ndim == 1:
            pts = pts.reshape(1, -1)
        return pts.astype(float)
    except Exception:
        # Try parsing as PCD format
        try:
            return load_pcd_format(pcd_path)
        except Exception as e2:
            logger.error(f"Failed to load point cloud from {pcd_path}: {e2}")
            return np.empty((0,3), dtype=float)


def load_pcd_format(pcd_path: Path) -> np.ndarray:
    """Load XYZ coordinates from PCD format file."""
    points = []
    data_started = False

    with open(pcd_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('DATA'):
                data_started = True
                continue

            if data_started and line:
                try:
                    # Split line and take first 3 values as x, y, z
                    parts = line.split()
                    if len(parts) >= 3:
                        x, y, z = float(parts[0]), float(parts[1]), float(parts[2])
                        points.append([x, y, z])
                except ValueError:
                    continue  # Skip invalid lines

    if not points:
        logger.warning(f"No valid points found in PCD file: {pcd_path}")
        return np.empty((0,3), dtype=float)

    return np.array(points, dtype=float)


def process_single_object(obj, cams, depth_imgs, cfg, args, fov_cfg, sectors, cam_fov_rad, use_corner, camera_corrector=None, drawn_images=None):
    """
    Process visibility calculation for a single object.

    PHASE 4 OPTIMIZATION: Extracted for parallel processing.
    Each object's visibility calculation is independent and can be parallelized.

    Args:
        obj: Object dictionary from JSON
        cams: Dictionary of camera objects
        depth_imgs: Pre-computed depth images for all cameras
        cfg: Configuration dictionary
        args: Command line arguments
        fov_cfg: FOV configuration
        sectors: Ego sector configuration
        cam_fov_rad: Camera FOV in radians
        use_corner: Whether to use corner-based FOV checking
        drawn_images: Optional images for visualization

    Returns:
        (obj_id, obj_aug): Tuple of object ID and augmented visibility data
    """
    try:
        obj_id = obj.get('ObjectID', 'unknown')

        # Extract 3D bounding box
        center = obj['3Dcenter']
        size = obj['3Dsize']
        cx, cy, cz = center['x'], center['y'], center['z']
        l, w, h = size['length'], size['width'], size['height']
        alpha = size.get('alpha', 0.0)

        # Sample surface points
        use_adaptive = cfg.get('surface_sampling', {}).get('adaptive_sampling', False)
        if use_adaptive:
            # For adaptive sampling, we need camera position - use first available camera
            cam_pos = None
            for cam_name, cam in cams.items():
                if cam is not None:
                    cam_pos = cam.T_base_cam[:3, 3]  # Camera position in base frame
                    break

            if cam_pos is not None:
                surf = sample_box_surface_adaptive((cx, cy, cz), l, w, h, alpha, cam_pos,
                                                 target_samples=cfg.get('surface_sampling', {}).get('max_samples_per_object', 4800))
            else:
                # Fallback to uniform sampling
                surf = sample_box_surface((cx, cy, cz), l, w, h, alpha, samples_per_face=800)
        else:
            surf = sample_box_surface((cx, cy, cz), l, w, h, alpha, samples_per_face=800)

        box = {'surface_pts': surf}

        corners = box_corners((cx, cy, cz), l, w, h, alpha)

        # Process each camera
        per_cam = {}
        stats_total = {'samples': 0, 'hits': 0}

        for cam_name, cam in cams.items():
            # FOV gating
            gate_ok = True
            point_gate_passed = False
            corner_gate_passed = False
            fov_fallback_used = False
            if fov_cfg:
                ok1, r1 = ego_sector_gate((cx,cy,cz), cam_name, sectors, ego_corrector=camera_corrector)
                if not ok1:
                    gate_ok = False
                else:
                    # Soft margin (radians) for FOV boundaries
                    import math
                    soft_margin_rad = math.radians(cfg.get('fov_filter', {}).get('soft_margin_deg', 0.0))
                    if use_corner:
                        ok2, r2 = cam_fov_gate_corners(corners, cam.T_base_cam, cam_name, cam_fov_rad, soft_margin_rad=soft_margin_rad, ego_corrector=camera_corrector)
                        corner_gate_passed = ok2
                    else:
                        ok2, r2 = cam_fov_gate_point((cx,cy,cz), cam.T_base_cam, cam_name, cam_fov_rad, soft_margin_rad=soft_margin_rad, ego_corrector=camera_corrector)
                        point_gate_passed = ok2
                        # Fallback: if point gate fails, try corners when enabled
                        if (not ok2) and cfg.get('fov_filter', {}).get('fallback_on_filter', False):
                            ok3, r3 = cam_fov_gate_corners(corners, cam.T_base_cam, cam_name, cam_fov_rad, soft_margin_rad=soft_margin_rad, ego_corrector=camera_corrector)
                            if ok3:
                                fov_fallback_used = True
                                corner_gate_passed = True
                                ok2 = True
                    if not ok2:
                        gate_ok = False
            # Optional diagnostics for FOV gating decisions
            if cfg.get('visibility', {}).get('enable_diagnostics', False) and cfg.get('fov_filter', {}).get('debug_filtering', False):
                logger.debug({
                    'obj_id': obj_id,
                    'camera': cam_name,
                    'point_gate_passed': point_gate_passed,
                    'corner_gate_passed': corner_gate_passed,
                    'fov_fallback_used': fov_fallback_used,
                    'final_decision': gate_ok
                })


            if not gate_ok:
                per_cam[cam_name] = None
                continue

            D, hits, uvz_hits = depth_imgs.get(cam_name, (None, None, None))
            if D is None:
                per_cam[cam_name] = None
                continue


            # Calculate visibility using safe configuration access
            from ..config.safe_access import get_safe_config_value
            enable_diagnostics = get_safe_config_value(cfg, 'visibility.enable_diagnostics', False, bool)
            debug_dir = get_safe_config_value(cfg, 'visibility.diagnostic_output_dir', 'outputs/diagnostics', str) if enable_diagnostics else None

            # Compute visibility via sphere method
            r, st = compute_visibility(
                box, cam, D, cfg, args.method, 0,
                enable_diagnostics, debug_dir, obj_id, cam_name
            )

            per_cam[cam_name] = r
            stats_total['samples'] += st.get('samples', 0)
            stats_total['hits'] += st.get('hits', 0)

            # Visualization (if enabled and images available)
            if args.viz_cam and drawn_images and cam_name in drawn_images:
                img = drawn_images[cam_name]
                # Note: Skip point cloud visualization in parallel processing for now
                # draw_projected_points(img, cam, pts)  # pts not available in this context
                draw_box_projections(img, cam, corners)
                overlay_visibility_score(img, r)

        # Fuse camera results with conservative strategy
        # TRAE-MOD START [20250825-1045-t2-safeget]: Purpose - avoid KeyError when visibility.include_noFOV_as_zero is missing in config; default to False to match fusion.py API
        # Enhanced with conservative fusion that prioritizes vis_max to reduce false negatives
        from ..config.safe_access import get_safe_config_value
        conservative_fusion_enabled = get_safe_config_value(cfg, 'conservative_validation.enabled', False, bool)
        vis_avg, vis_max, visible_views, fusion_stats = fuse_camera_visibility(
            per_cam, 
            include_noFOV_as_zero=get_safe_config_value(cfg, 'visibility.include_noFOV_as_zero', False, bool),
            conservative_fusion=conservative_fusion_enabled
        )
        # TRAE-MOD END [20250825-1045-t2-safeget]
        # Use conservative fusion strategy: prioritize vis_max for occlusion level when conservative fusion is enabled
        primary_visibility = fusion_stats['primary_visibility'] if conservative_fusion_enabled else vis_avg
        level = to_occlusion_level(primary_visibility)

        # Apply consistency validation using primary visibility metric
        is_consistent, corrected_level, reason = validate_occlusion_consistency(primary_visibility, visible_views, level)

        if not is_consistent:
            logger.info(f"Object {obj_id}: Occlusion level corrected from {level} to {corrected_level} ({reason})")
            level = corrected_level

        # Calculate additional statistics
        vis_stats = calculate_visibility_statistics(per_cam)

        # Log interesting cases with fusion information
        if primary_visibility > 0.05 or level <= 2:
            logger.info(f"Object {obj_id}: vis_avg={vis_avg:.4f}, vis_max={vis_max:.4f}, "
                       f"primary={primary_visibility:.4f} ({fusion_stats['primary_metric']}), level={level}, "
                       f"visible_views={len(visible_views)}, coverage={vis_stats['coverage_ratio']:.2f}, "
                       f"fusion_method={fusion_stats['fusion_method']}")

        # Create output (extended fields)
        best_view = max(((k, v) for k, v in per_cam.items() if v is not None), key=lambda kv: kv[1], default=(None, 0.0))[0]

        # Calculate per-camera occlusion rates (1 - visibility_rate)
        per_cam_occlusion = {}
        for cam_name, vis_rate in per_cam.items():
            if vis_rate is not None:
                per_cam_occlusion[cam_name] = 1.0 - vis_rate
            else:
                per_cam_occlusion[cam_name] = None

        occlusion_rate_avg = 1.0 - vis_avg if vis_avg is not None else None

        obj_aug = {
            "visibility": {
                "per_camera": per_cam,
                "per_camera_occlusion": per_cam_occlusion,
                "visibility_rate_avg": vis_avg,
                "visibility_rate_max": vis_max,
                "visibility_rate_primary": primary_visibility,  # New field for conservative fusion
                "occlusion_rate_avg": occlusion_rate_avg,
                "occlusion_level": level,
                "visible_in_views": visible_views,
                "best_view": best_view,
                "fusion_stats": fusion_stats,  # Add fusion method tracking
            }
        }

        return obj_id, obj_aug

    except Exception as e:
        logger.error(f"Error processing object {obj.get('ObjectID', 'unknown')}: {e}")
        return obj.get('ObjectID', 'unknown'), None


def filter_objects_by_category(objects, allowed_categories=None):
    """
    Filter 3D detection objects by category labels for performance optimization.

    This function implements category filtering as the first step in performance optimization,
    reducing the number of objects that need to be processed through the expensive visibility
    calculation pipeline.

    Args:
        objects: List of object dictionaries from JSON data
        allowed_categories: List of allowed category labels (strings).
                          Defaults to autonomous driving categories: [1000, 2000, 3000, 4000, 5000, 6000, 7000]

    Returns:
        Tuple of (filtered_objects, filter_stats) where:
        - filtered_objects: List of objects that passed the category filter
        - filter_stats: Dictionary with filtering statistics
    """
    if allowed_categories is None:
        # Default autonomous driving categories
        allowed_categories = ['1000', '2000', '3000', '4000', '5000', '6000', '7000']

    # Convert to set for faster lookup
    allowed_set = set(str(cat) for cat in allowed_categories)

    filtered_objects = []
    total_objects = len(objects)

    for obj in objects:
        obj_label = str(obj.get('label', ''))
        if obj_label in allowed_set:
            filtered_objects.append(obj)

    filtered_count = len(filtered_objects)
    filtered_out_count = total_objects - filtered_count

    filter_stats = {
        'total_objects': total_objects,
        'filtered_objects': filtered_count,
        'filtered_out_objects': filtered_out_count,
        'filter_efficiency': filtered_out_count / total_objects if total_objects > 0 else 0.0,
        'allowed_categories': list(allowed_categories)
    }

    return filtered_objects, filter_stats


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--clip_dir', required=True)
    parser.add_argument('--config', default=str(Path(__file__).resolve().parents[2] / 'configs' / 'default.yaml'))
    parser.add_argument('--save_dir', default='./outputs')
    parser.add_argument('--viz_cam', action='store_true')
    parser.add_argument('--method', default='sphere', choices=['sphere'])
    args = parser.parse_args()

    # Load configuration with validation
    from ..config.loader import load_config, log_configuration_summary
    cfg = load_config(args.config, validate=True)
    log_configuration_summary(cfg)
    
    # Initialize camera position corrector
    camera_corrector = create_camera_corrector(cfg)
    if camera_corrector.enabled:
        camera_corrector.log_correction_summary()
    clip = ClipDataset(args.clip_dir)
    ts_list = clip.list_timestamps()

    calibs = parse_calibrated_sensor_pb_txt(str(clip.calib_pb_txt))
    # Removed pose loading - data is pre-synchronized

    cam_map = cfg.get('camera_map', {})
    cams = {}
    for dir_name, sensor_name in cam_map.items():
        if sensor_name not in calibs.cams:
            continue
        c = calibs.cams[sensor_name]
        cams[dir_name] = Camera(
            name=dir_name,
            K=c['K'],
            dist=c['dist'],
            width=c['width'],
            height=c['height'],
            T_base_cam=c['T_base_cam'],
        )

    save_root = Path(args.save_dir) / Path(args.clip_dir).name
    save_root.mkdir(parents=True, exist_ok=True)

    for ts in ts_list:
        paths = clip.frame_paths_by_ts(ts)
        pts = load_pcd_xyz(paths['pcd'])
        j = json.loads(Path(paths['json']).read_text(encoding='utf-8'))
        objects = j.get('result', {}).get('data', [])

        # PERFORMANCE OPTIMIZATION: Apply category filtering to reduce processing load
        # Filter objects by category labels to process only relevant autonomous driving objects
        filtered_objects, filter_stats = filter_objects_by_category(objects)

        # Log filtering statistics
        if filter_stats['filtered_out_objects'] > 0:
            logger.info(f"Frame {ts}: Category filtering - "
                       f"Total: {filter_stats['total_objects']}, "
                       f"Processed: {filter_stats['filtered_objects']}, "
                       f"Filtered out: {filter_stats['filtered_out_objects']} "
                       f"({filter_stats['filter_efficiency']:.1%} reduction)")
        else:
            logger.info(f"Frame {ts}: Processing all {filter_stats['total_objects']} objects "
                       f"(all match allowed categories)")

        # Use filtered objects for subsequent processing
        objects = filtered_objects

        # PHASE 4 OPTIMIZATION: Cache depth maps per camera (expensive operation)
        # Each camera's depth map is the same for all objects in this frame
        depth_imgs = {}
        images_cache = {}

        logger.info(f"Generating depth maps for {len(cams)} cameras...")
        depth_generation_start = time.time()

        for cam_name, cam in cams.items():
            img_path = paths['images'][cam_name]
            if not img_path.exists():
                logger.warning(f"Image not found for camera {cam_name}: {img_path}")
                continue

            H, W = cam.height, cam.width

            # Generate depth map once per camera (most expensive operation)
            # PERFORMANCE OPTIMIZATION: Pass camera name for spatial filtering
            cam_start_time = time.time()
            D, hits, uvz_hits = pointcloud_to_depth(pts, cam, H, W, camera_name=cam_name)

            # Apply depth dilation to address point cloud density issues
            D_dilated = apply_depth_dilation(D, dilation_size=3)

            depth_imgs[cam_name] = (D_dilated, hits, uvz_hits)

            cam_duration = time.time() - cam_start_time
            logger.info(f"Camera {cam_name}: depth map generated in {cam_duration:.2f}s ({hits} hits)")

        depth_generation_duration = time.time() - depth_generation_start
        logger.info(f"All depth maps generated in {depth_generation_duration:.2f}s")

        # Load images for visualization if needed
        if args.viz_cam:
            for cam_name, cam in cams.items():
                img_path = paths['images'][cam_name]
                if img_path.exists():
                    img = cv2.imread(str(img_path))
                    if img is not None:
                        images_cache[cam_name] = img

        per_object_aug = {}
        # Prepare per-camera images for drawing once and overlay multiple objects
        drawn_images = {k: v.copy() for k, v in images_cache.items()}

        # PHASE 4 OPTIMIZATION: Parallel processing of objects
        logger.info(f"Processing {len(objects)} objects in parallel...")
        object_processing_start = time.time()

        # Prepare common parameters for all objects
        fov_cfg = cfg.get('fov_filter', {}).get('enabled', True)
        use_corner = cfg.get('fov_filter', {}).get('use_corner_check', False)
        cam_fovs_deg = cfg.get('camera_fovs_deg', {})
        cam_fov_rad = {k: {"h": v.get('h', 120.0)*np.pi/180.0, "v": v.get('v', cfg.get('fov_filter',{}).get('vfov_deg_default',40.0))*np.pi/180.0} for k,v in cam_fovs_deg.items()}
        sector_map = cfg.get('ego_sector_map_deg', {})
        sectors = {k: {"center": v['center']*np.pi/180.0, "half": v['half']*np.pi/180.0} for k,v in sector_map.items()}

        # Determine number of worker threads (use CPU count but limit to reasonable number)
        import os
        max_workers = min(len(objects), os.cpu_count() or 4, 8)  # Limit to 8 threads max
        logger.info(f"Using {max_workers} worker threads for parallel processing")

        # Process objects in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all objects for processing
            future_to_obj = {
                executor.submit(
                    process_single_object,
                    obj, cams, depth_imgs, cfg, args, fov_cfg, sectors, cam_fov_rad, use_corner, camera_corrector, drawn_images
                ): obj for obj in objects
            }

            # Collect results as they complete
            completed_objects = 0
            for future in as_completed(future_to_obj):
                obj = future_to_obj[future]
                try:
                    obj_id, obj_aug = future.result()
                    if obj_aug is not None:
                        per_object_aug[obj_id] = obj_aug
                    completed_objects += 1

                    # Log progress every 10 objects
                    if completed_objects % 10 == 0:
                        logger.info(f"Completed {completed_objects}/{len(objects)} objects")

                except Exception as e:
                    logger.error(f"Error processing object {obj.get('ObjectID', 'unknown')}: {e}")

        object_processing_duration = time.time() - object_processing_start
        logger.info(f"All {len(objects)} objects processed in {object_processing_duration:.2f}s "
                   f"({object_processing_duration/len(objects):.3f}s per object)")

        # Note: The old sequential processing loop has been replaced with parallel processing above

        out_json = save_root / f"{ts}.json"
        from .integrate_json import write_augmented_json
        write_augmented_json(str(paths['json']), str(out_json), per_object_aug)

        # Save per-camera images for this ts
        if args.viz_cam and drawn_images:
            for cam_name, img in drawn_images.items():
                out_img = save_root / 'viz' / cam_name / f"{ts}.jpg"
                save_image(img, str(out_img))
            # optional mosaic across cameras
            imgs_in_order = [drawn_images[k] for k in sorted(drawn_images.keys())]
            mosaic = make_mosaic(imgs_in_order, cols=4)
            if mosaic is not None:
                save_image(mosaic, str(save_root / 'viz' / f"{ts}_mosaic.jpg"))

    print(f"Done: outputs saved to {save_root}")

if __name__ == '__main__':
    main()
