#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bird's Eye View (BEV) Visualization Script for Occlusion Results

This script processes JSON result files and renders <PERSON>'s Eye View diagrams
showing all 3D objects with occlusion levels and rates in a top-down perspective.

Author: <PERSON>rae AI Agent
Date: 2025-08-25
"""

import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
import yaml
from tqdm import tqdm
import glob

# TRAE-MOD START [20250825-1200-bev-vis]: Purpose - implement BEV visualization for comprehensive occlusion analysis

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BatchBEVVisualizationProcessor:
    """
    Processes <PERSON>'s Eye View visualization of occlusion results for all objects in a scene.
    """
    
    def __init__(self, 
                 result_dir: str,
                 output_dir: str,
                 config_path: str = 'configs/default.yaml'):
        """
        Initialize the BEV visualization processor.
        
        Args:
            result_dir: Directory containing JSON result files
            output_dir: Directory to save BEV visualization results
            config_path: Path to configuration file (optional)
        """
        self.result_dir = Path(result_dir)
        self.output_dir = Path(output_dir)
        
        # Load configuration (optional, for any future parameters)
        self.config = {}
        if Path(config_path).exists():
            try:
                self.config = yaml.safe_load(open(config_path, 'r'))
            except Exception as e:
                logger.warning(f"Could not load config {config_path}: {e}")
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
# TRAE-MOD START [20250825-1400-bev-opt]: Purpose - optimize BEV dimensions and scaling for clarity
        # BEV parameters - optimized for proper aspect ratio and clarity
        self.pixels_per_m = 5.0  # Fixed pixels per meter for clarity
        self.y_range = (-100, 100)  # meters, left-right
        self.x_range = (-180, 180)  # meters, forward-backward
        
        # Calculate dimensions based on ranges and fixed scale
        y_span = self.y_range[1] - self.y_range[0]  # 200m
        x_span = self.x_range[1] - self.x_range[0]  # 360m
        self.bev_width = int(y_span * self.pixels_per_m)   # 1000 pixels (Y horizontal)
        self.bev_height = int(x_span * self.pixels_per_m)  # 1800 pixels (X vertical)
# TRAE-MOD END [20250825-1400-bev-opt]
        
        # Grid parameters
        self.grid_spacing = 10  # meters
        
        # Ego vehicle parameters (typical car dimensions)
        self.ego_length = 5.0  # meters (X direction)
        self.ego_width = 2.0   # meters (Y direction)
        
        logger.info(f"Initialized BatchBEVVisualizationProcessor")
        logger.info(f"Result dir: {self.result_dir}")
        logger.info(f"Output dir: {self.output_dir}")
        logger.info(f"BEV image size: {self.bev_width}x{self.bev_height}")
        logger.info(f"Scale: {self.pixels_per_m:.2f} pixels/meter")
    
    def _world_to_pixel(self, x: float, y: float) -> Tuple[int, int]:
        """
        Convert world coordinates (vehicle frame) to pixel coordinates.
        
        Args:
            x: X coordinate in vehicle frame (forward positive)
            y: Y coordinate in vehicle frame (left positive)
            
        Returns:
            Tuple of (pixel_x, pixel_y) coordinates
        """
        # Map world coordinates to pixel coordinates
        # X (forward) maps to vertical axis (top = forward)
        # Y (left) maps to horizontal axis (left = left)
        
        pixel_x = int((y - self.y_range[0]) * self.pixels_per_m)
        pixel_y = int((self.x_range[1] - x) * self.pixels_per_m)  # Flip Y axis (top = forward)
        
        return pixel_x, pixel_y
    
    def _load_json_data(self, json_file: Path) -> Optional[Dict]:
        """
        Load and parse JSON result file.
        
        Args:
            json_file: Path to JSON file
            
        Returns:
            Parsed JSON data or None if failed
        """
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            return data
        except Exception as e:
            logger.error(f"Failed to load {json_file}: {e}")
            return None
    
    def _create_2d_box_from_json(self, obj_data: Dict) -> Optional[Dict]:
        """
        Extract 2D box parameters from JSON object data.
        
        Args:
            obj_data: Object data from JSON
            
        Returns:
            Dictionary with 2D box parameters or None if invalid
        """
        try:
            # Extract 3D center and size
            center_3d = obj_data.get('3Dcenter', {})
            size_3d = obj_data.get('3Dsize', {})
            visibility = obj_data.get('visibility', {})
            
            # Required fields
            x = center_3d.get('x')
            y = center_3d.get('y')
            length = size_3d.get('length')  # X direction
            width = size_3d.get('width')    # Y direction
            
            # Optional rotation
            rz = size_3d.get('rz', 0.0)  # Yaw rotation
            if rz is None:
                rz = size_3d.get('alpha', 0.0)  # Alternative field name
            if rz is None:
                rz = 0.0
            
            # Visibility fields
            occlusion_level = visibility.get('occlusion_level')
            occlusion_rate_avg = visibility.get('occlusion_rate_avg')
            
            if any(v is None for v in [x, y, length, width, occlusion_level, occlusion_rate_avg]):
                logger.warning(f"Missing required fields in object: {obj_data.get('ObjectID', 'unknown')}")
                return None
            
            return {
                'center_x': x,
                'center_y': y,
                'length': length,
                'width': width,
                'yaw': rz,
                'occlusion_level': occlusion_level,
                'occlusion_rate_avg': occlusion_rate_avg,
                'object_id': obj_data.get('ObjectID', 'unknown'),
                'label': obj_data.get('label', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Failed to parse object data: {e}")
            return None
    
    def _get_box_corners_2d(self, box_data: Dict) -> np.ndarray:
        """
        Compute 2D corners of rotated bounding box.
        
        Args:
            box_data: Box parameters from _create_2d_box_from_json
            
        Returns:
            Array of shape (4, 2) with corner coordinates
        """
        cx, cy = box_data['center_x'], box_data['center_y']
        length, width = box_data['length'], box_data['width']
        yaw = box_data['yaw']
        
        # Local corners (before rotation and translation)
        # Length is X direction, width is Y direction
        half_length = length / 2
        half_width = width / 2
        
        local_corners = np.array([
            [-half_length, -half_width],  # rear-right
            [half_length, -half_width],   # front-right
            [half_length, half_width],    # front-left
            [-half_length, half_width]    # rear-left
        ])
        
        # Rotation matrix
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        rotation_matrix = np.array([
            [cos_yaw, -sin_yaw],
            [sin_yaw, cos_yaw]
        ])
        
        # Apply rotation and translation
        rotated_corners = local_corners @ rotation_matrix.T
        world_corners = rotated_corners + np.array([cx, cy])
        
        return world_corners
    
# TRAE-MOD START [20250825-1400-bev-opt]: Purpose - refine color mapping for occlusion levels
    def _get_occlusion_color(self, occlusion_level: int) -> Tuple[int, int, int]:
        """
        Get color for occlusion level (BGR format for OpenCV).
        
        Args:
            occlusion_level: Occlusion level (0-4)
            
        Returns:
            BGR color tuple
        """
        if occlusion_level == 0:
            return (128, 128, 128)  # Gray for no occlusion data
        elif occlusion_level <= 2:
            return (0, 255, 0)      # Green for low occlusion (1-2)
        elif occlusion_level == 3:
            return (0, 255, 255)    # Yellow for medium occlusion
        else:  # occlusion_level >= 4
            return (0, 0, 255)      # Red for high occlusion
# TRAE-MOD END [20250825-1400-bev-opt]
    
    def _draw_grid(self, img: np.ndarray) -> None:
        """
        Draw grid lines and labels on BEV image.
        
        Args:
            img: Image array to draw on
        """
        # Grid color (light gray)
        grid_color = (200, 200, 200)
        text_color = (100, 100, 100)
        
        # Vertical lines (Y axis ticks)
        for y_world in range(int(self.y_range[0]), int(self.y_range[1]) + 1, self.grid_spacing):
            pixel_x, _ = self._world_to_pixel(0, y_world)
            if 0 <= pixel_x < self.bev_width:
                cv2.line(img, (pixel_x, 0), (pixel_x, self.bev_height), grid_color, 1)
                # Label
                if y_world % (self.grid_spacing * 2) == 0:  # Every 20m
                    cv2.putText(img, f"{y_world}", (pixel_x - 10, self.bev_height - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, text_color, 1)
        
        # Horizontal lines (X axis ticks)
        for x_world in range(int(self.x_range[0]), int(self.x_range[1]) + 1, self.grid_spacing):
            _, pixel_y = self._world_to_pixel(x_world, 0)
            if 0 <= pixel_y < self.bev_height:
                cv2.line(img, (0, pixel_y), (self.bev_width, pixel_y), grid_color, 1)
                # Label
                if x_world % (self.grid_spacing * 2) == 0:  # Every 20m
                    cv2.putText(img, f"{x_world}", (10, pixel_y + 5),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, text_color, 1)
    
    def _draw_ego_vehicle(self, img: np.ndarray) -> None:
        """
        Draw ego vehicle at origin.
        
        Args:
            img: Image array to draw on
        """
        # Ego vehicle corners
        ego_corners = np.array([
            [-self.ego_length/2, -self.ego_width/2],
            [self.ego_length/2, -self.ego_width/2],
            [self.ego_length/2, self.ego_width/2],
            [-self.ego_length/2, self.ego_width/2]
        ])
        
        # Convert to pixels
        pixel_corners = []
        for corner in ego_corners:
            px, py = self._world_to_pixel(corner[0], corner[1])
            pixel_corners.append([px, py])
        
        pixel_corners = np.array(pixel_corners, dtype=np.int32)
        
        # Draw filled rectangle (blue)
        cv2.fillPoly(img, [pixel_corners], (255, 0, 0))  # Blue in BGR
        cv2.polylines(img, [pixel_corners], True, (200, 0, 0), 2)  # Darker blue border
    
# TRAE-MOD START [20250825-1400-bev-opt]: Purpose - remove labels and increase line thickness for clarity
    def _draw_object_box(self, img: np.ndarray, box_data: Dict) -> None:
        """
        Draw object bounding box with color-coded occlusion level (no labels).
        
        Args:
            img: Image array to draw on
            box_data: Box parameters
        """
        # Get corners and convert to pixels
        world_corners = self._get_box_corners_2d(box_data)
        pixel_corners = []
        
        for corner in world_corners:
            px, py = self._world_to_pixel(corner[0], corner[1])
            pixel_corners.append([px, py])
        
        pixel_corners = np.array(pixel_corners, dtype=np.int32)
        
        # Check if box is within image bounds
        if not any(0 <= px < self.bev_width and 0 <= py < self.bev_height 
                  for px, py in pixel_corners):
            return  # Skip boxes completely outside image
        
        # Get color based on occlusion level
        color = self._get_occlusion_color(box_data['occlusion_level'])
        
        # Draw box outline with increased thickness for clarity
        cv2.polylines(img, [pixel_corners], True, color, 3)
# TRAE-MOD END [20250825-1400-bev-opt]
    
    def _render_bev_frame(self, data: Dict, timestamp: str) -> Optional[np.ndarray]:
        """
        Render BEV visualization for a single frame.
        
        Args:
            data: JSON data for the frame
            timestamp: Timestamp string for the frame
            
        Returns:
            Rendered BEV image or None if failed
        """
        try:
            # Create blank image (white background)
            img = np.ones((self.bev_height, self.bev_width, 3), dtype=np.uint8) * 255
            
            # Draw grid
            self._draw_grid(img)
            
            # Draw ego vehicle
            self._draw_ego_vehicle(img)
            
            # Process objects
            objects_data = data.get('result', {}).get('data', [])
            valid_objects = 0
            
            for obj_data in objects_data:
                box_data = self._create_2d_box_from_json(obj_data)
                if box_data is not None:
                    self._draw_object_box(img, box_data)
                    valid_objects += 1
            
            logger.info(f"Rendered {valid_objects}/{len(objects_data)} objects for {timestamp}")
            return img
            
        except Exception as e:
            logger.error(f"Failed to render BEV for {timestamp}: {e}")
            return None
    
    def process_all(self) -> None:
        """
        Process all JSON files and generate BEV visualizations.
        """
        # Find all JSON files
        json_files = list(self.result_dir.glob("*.json"))
        
        if not json_files:
            logger.warning(f"No JSON files found in {self.result_dir}")
            return
        
        logger.info(f"Found {len(json_files)} JSON files to process")
        
        success_count = 0
        
        # Process each file
        for json_file in tqdm(json_files, desc="Processing BEV visualizations"):
            # Extract timestamp from filename
            timestamp = json_file.stem
            
            # Load JSON data
            data = self._load_json_data(json_file)
            if data is None:
                continue
            
            # Render BEV
            bev_img = self._render_bev_frame(data, timestamp)
            if bev_img is None:
                continue
            
            # Save image
            output_file = self.output_dir / f"{timestamp}_bev.png"
            try:
                cv2.imwrite(str(output_file), bev_img)
                success_count += 1
            except Exception as e:
                logger.error(f"Failed to save {output_file}: {e}")
        
        logger.info(f"Successfully processed {success_count}/{len(json_files)} files")
        logger.info(f"BEV visualizations saved to: {self.output_dir}")

# TRAE-MOD END [20250825-1200-bev-vis]

def main():
    """
    Main function to run BEV visualization with default paths.
    """
    result_dir = "./outputs/clip_dataset_2/clip_dataset_2"  
    output_dir = "./outputs/clip_dataset_2/bev_vis"
    
    processor = BatchBEVVisualizationProcessor(
        result_dir=result_dir,
        output_dir=output_dir
    )
    
    processor.process_all()

if __name__ == "__main__":
    main()