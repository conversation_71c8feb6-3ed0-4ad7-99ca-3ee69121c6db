#!/usr/bin/env python3
"""
Test script to verify configuration validation for conservative validation parameters.
"""

import sys
import logging
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from robobus_vis.config.loader import load_config, load_config_safe
from robobus_vis.config.validation import (
    validate_full_config, 
    apply_conservative_defaults
)
from robobus_vis.config.safe_access import (
    get_safe_config_value,
    validate_configuration_startup,
    log_configuration_access_summary
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def test_default_config():
    """Test loading and validating the default configuration."""
    logger.info("=== Testing Default Configuration ===")
    
    try:
        config = load_config('configs/default.yaml')
        logger.info("✓ Default configuration loaded successfully")
        
        # Check conservative validation parameters
        cv_enabled = get_safe_config_value(config, 'conservative_validation.enabled', None)
        azimuth_tol = get_safe_config_value(config, 'conservative_validation.azimuth_tolerance_deg', None)
        min_points = get_safe_config_value(config, 'conservative_validation.min_depth_points', None)
        tau_mult = get_safe_config_value(config, 'conservative_validation.tau_multiplier', None)
        
        logger.info(f"Conservative validation enabled: {cv_enabled}")
        logger.info(f"Azimuth tolerance: {azimuth_tol}°")
        logger.info(f"Min depth points: {min_points}")
        logger.info(f"Tau multiplier: {tau_mult}")
        
        # Check FOV filter parameters
        soft_margin = get_safe_config_value(config, 'fov_filter.soft_margin_deg', None)
        logger.info(f"FOV soft margin: {soft_margin}°")
        
        # Check visibility parameters
        treat_no_depth = get_safe_config_value(config, 'visibility.treat_no_depth_as_visible', None)
        logger.info(f"Treat no depth as visible: {treat_no_depth}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Failed to load default configuration: {e}")
        return False


def test_invalid_config():
    """Test validation with invalid configuration parameters."""
    logger.info("=== Testing Invalid Configuration ===")
    
    # Create invalid configuration
    invalid_config = {
        'conservative_validation': {
            'enabled': 'not_a_boolean',  # Invalid type
            'azimuth_tolerance_deg': -5.0,  # Invalid range
            'min_depth_points': 0,  # Invalid range
            'tau_multiplier': 10.0  # Invalid range
        },
        'fov_filter': {
            'soft_margin_deg': 50.0  # Invalid range
        },
        'visibility': {
            'treat_no_depth_as_visible': 'not_a_boolean',  # Invalid type
            'tau_base_m': -1.0  # Invalid range
        }
    }
    
    is_valid, errors = validate_full_config(invalid_config)
    
    if not is_valid:
        logger.info("✓ Invalid configuration correctly rejected")
        for error in errors:
            logger.info(f"  - {error}")
        return True
    else:
        logger.error("✗ Invalid configuration was incorrectly accepted")
        return False


def test_safe_defaults():
    """Test that safe defaults are applied correctly."""
    logger.info("=== Testing Safe Defaults ===")
    
    # Empty configuration
    empty_config = {}
    
    config_with_defaults = apply_conservative_defaults(empty_config)
    
    # Check that defaults were applied
    cv_enabled = get_safe_config_value(config_with_defaults, 'conservative_validation.enabled', None)
    azimuth_tol = get_safe_config_value(config_with_defaults, 'conservative_validation.azimuth_tolerance_deg', None)
    
    if cv_enabled is not None and azimuth_tol is not None:
        logger.info("✓ Safe defaults applied correctly")
        logger.info(f"Default conservative validation enabled: {cv_enabled}")
        logger.info(f"Default azimuth tolerance: {azimuth_tol}°")
        return True
    else:
        logger.error("✗ Safe defaults not applied correctly")
        return False


def test_safe_config_loading():
    """Test safe configuration loading with fallback."""
    logger.info("=== Testing Safe Configuration Loading ===")
    
    # Test with non-existent file
    config = load_config_safe('non_existent_config.yaml')
    
    if config is not None:
        logger.info("✓ Safe loading with fallback works")
        cv_enabled = get_safe_config_value(config, 'conservative_validation.enabled', None)
        logger.info(f"Fallback conservative validation enabled: {cv_enabled}")
        return True
    else:
        logger.error("✗ Safe loading failed")
        return False


def main():
    """Run all configuration validation tests."""
    logger.info("Starting configuration validation tests...")
    
    tests = [
        test_default_config,
        test_invalid_config,
        test_safe_defaults,
        test_safe_config_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            logger.info("")  # Empty line for readability
        except Exception as e:
            logger.error(f"Test {test.__name__} failed with exception: {e}")
            logger.info("")
    
    logger.info(f"=== Test Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        logger.info("All tests passed! Configuration validation is working correctly.")
        return 0
    else:
        logger.error("Some tests failed. Please check the configuration validation implementation.")
        return 1


if __name__ == '__main__':
    sys.exit(main())