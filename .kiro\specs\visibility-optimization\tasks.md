# Implementation Plan

- [x] 1. Create conservative validation module with core functions

  - Implement `is_in_camera_fov_loose` function for generous FOV checking
  - Implement `conservative_depth_validation` function for sparse-data-aware depth analysis
  - Add comprehensive unit tests for edge cases (empty depth maps, extreme tolerances)
  - _Requirements: 1.1, 1.4, 3.1, 5.1_

- [x] 2. Enhance visibility dispatcher to integrate conservative validation

  - Modify `compute_visibility` function in `dispatcher.py` to call conservative validation when enabled
  - Add logic to combine sphere projection and conservative validation scores using max function
  - Please refer to this markdown record (Sphere_Projection_Visibility_Algorithm.md) of the algorithm implementation that computes solely through the sphere projection of 3D detection boxes. This algorithm is excerpted from the reference paper.
  - Ensure backward compatibility by making conservative validation optional via configuration
  - Add diagnostic statistics to track both sphere and conservative visibility scores
  - _Requirements: 1.1, 1.2, 2.1, 2.4, 5.2_

- [x] 3. Update fusion module to use maximum-based strategy

  - Modify `fuse_camera_visibility` function in `fusion.py` to prioritize `vis_max` over `vis_avg`
  - Implement conservative fusion logic that takes maximum visibility across cameras
  - Maintain existing visible views calculation with conservative threshold

  - Add fusion method tracking in output statistics
  - _Requirements: 2.2, 2.3, 5.2_

- [x] 4. Add configuration parameters for conservative validation

  - Extend `configs/default.yaml` with `conservative_validation` section
  - Add parameters for azimuth tolerance, minimum depth points, and tau multiplier
  - Update existing `fov_filter` and `visibility` sections with conservative defaults
  - Implement configuration validation to ensure parameter sanity
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 5. Implement safe configuration access patterns

  - Add utility functions for safe configuration parameter retrieval with defaults
  - Update all configuration access points to use safe-get patterns
  - Add configuration validation at system startup
  - Implement graceful degradation when conservative validation config is invalid
  - _Requirements: 3.4, 5.4_

- [x] 6. Create comprehensive unit tests for conservative validation


  - Write tests for `is_in_camera_fov_loose` with various tolerance values and camera orientations
  - Write tests for `conservative_depth_validation` with sparse, dense, and empty depth data
  - Create integration tests for dispatcher with conservative validation enabled/disabled
  - Add tests for fusion logic with different camera configurations
  - _Requirements: 5.1, 5.3_

- [ ] 7. Add performance monitoring and diagnostics
  - Instrument conservative validation functions with timing measurements
  - Add diagnostic output fields to JSON format for validation decisions
  - Implement logging for sparse depth data detection and conservative decisions
  - Ensure existing performance instrumentation continues to work
  - _Requirements: 4.1, 4.4, 5.4_

- [ ] 8. Validate system performance and accuracy
  - Run performance benchmarks comparing baseline vs conservative validation enabled
  - Measure processing time impact and ensure it stays within 20% degradation limit
  - Test parallel processing functionality with ThreadPoolExecutor
  - Verify per-frame, per-camera depth map caching still functions efficiently
  - _Requirements: 4.1, 4.2, 4.3_

- [ ] 9. Test false negative reduction on validation datasets
  - Run system on `visibility_demo_data` with conservative validation enabled
  - Compare visibility scores and false negative rates before and after enhancement
  - Validate that objects with environmental occlusions get improved visibility scores
  - Test edge cases with sparse point clouds and calibration errors
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 10. Ensure backward compatibility and integration
  - Verify existing JSON output format is preserved with additional diagnostic fields
  - Test that system works correctly with conservative validation disabled
  - Validate that existing API contracts for visibility functions remain unchanged
  - Run existing test suite to ensure no regressions
  - _Requirements: 2.4, 3.4, 5.2_

- [ ] 11. Update documentation and add code comments
  - Add comprehensive docstrings to all new functions in conservative validation module
  - Update inline comments explaining conservative validation logic in dispatcher and fusion
  - Document new configuration parameters and their effects
  - Add examples of conservative validation usage in code comments
  - _Requirements: 5.1, 5.4_

- [ ] 12. Integration testing with full pipeline
  - Test conservative validation with `run_batch.py` pipeline on sample clips
  - Verify visualization outputs work correctly with enhanced visibility scores
  - Test configuration loading and parameter validation in full pipeline context
  - Validate that batch processing maintains efficiency with conservative validation enabled
  - _Requirements: 4.1, 4.4, 5.3_
