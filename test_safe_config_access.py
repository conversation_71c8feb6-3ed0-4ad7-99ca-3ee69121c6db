#!/usr/bin/env python3
"""
Test safe configuration access patterns for conservative validation.

This test validates that the safe configuration access utilities work correctly
and provide graceful degradation when configuration parameters are missing or invalid.
"""

import sys
import os
import tempfile
import yaml
import logging
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

from robobus_vis.config.safe_access import (
    get_safe_config_value,
    get_conservative_validation_config,
    get_visibility_config,
    get_fov_filter_config,
    get_camera_config,
    get_camera_fov_config,
    validate_configuration_startup,
    create_minimal_config,
    handle_configuration_degradation,
    log_configuration_access_summary
)
from robobus_vis.config.loader import load_config_safe, load_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_safe_config_value():
    """Test get_safe_config_value function with various scenarios."""
    print("Testing get_safe_config_value...")
    
    config = {
        'visibility': {
            'tau_base_m': 2.0,
            'enabled': True
        },
        'conservative_validation': {
            'enabled': False,
            'azimuth_tolerance_deg': 15.0
        }
    }
    
    # Test normal access
    assert get_safe_config_value(config, 'visibility.tau_base_m', 1.0) == 2.0
    assert get_safe_config_value(config, 'conservative_validation.enabled', True) == False
    
    # Test missing key with default
    assert get_safe_config_value(config, 'missing.key', 'default') == 'default'
    
    # Test type validation
    assert get_safe_config_value(config, 'visibility.enabled', False, bool) == True
    
    # Test invalid type (should return default)
    config_invalid = {'visibility': {'enabled': 'not_a_bool'}}
    assert get_safe_config_value(config_invalid, 'visibility.enabled', False, bool) == False
    
    # Test with validator
    def validate_positive(value):
        return (value > 0, "Must be positive")
    
    assert get_safe_config_value(config, 'visibility.tau_base_m', 1.0, float, validate_positive) == 2.0
    
    # Test validator failure
    config_negative = {'visibility': {'tau_base_m': -1.0}}
    assert get_safe_config_value(config_negative, 'visibility.tau_base_m', 1.0, float, validate_positive) == 1.0
    
    print("✓ get_safe_config_value tests passed")


def test_conservative_validation_config():
    """Test get_conservative_validation_config function."""
    print("Testing get_conservative_validation_config...")
    
    # Test with complete config
    config = {
        'conservative_validation': {
            'enabled': True,
            'azimuth_tolerance_deg': 15.0,
            'min_depth_points': 10,
            'tau_multiplier': 2.0
        }
    }
    
    cv_config = get_conservative_validation_config(config)
    assert cv_config['enabled'] == True
    assert cv_config['azimuth_tolerance_deg'] == 15.0
    assert cv_config['min_depth_points'] == 10
    assert cv_config['tau_multiplier'] == 2.0
    
    # Test with missing section (should use defaults)
    empty_config = {}
    cv_config = get_conservative_validation_config(empty_config)
    assert cv_config['enabled'] == False
    assert cv_config['azimuth_tolerance_deg'] == 10.0
    assert cv_config['min_depth_points'] == 5
    assert cv_config['tau_multiplier'] == 1.5
    
    # Test with invalid values (should use defaults)
    invalid_config = {
        'conservative_validation': {
            'enabled': 'not_bool',
            'azimuth_tolerance_deg': -10,  # Invalid
            'min_depth_points': 0,  # Invalid
            'tau_multiplier': 10.0  # Invalid (too high)
        }
    }
    cv_config = get_conservative_validation_config(invalid_config)
    assert cv_config['enabled'] == False  # Default due to invalid type
    assert cv_config['azimuth_tolerance_deg'] == 10.0  # Default due to invalid range
    assert cv_config['min_depth_points'] == 5  # Default due to invalid range
    assert cv_config['tau_multiplier'] == 1.5  # Default due to invalid range
    
    print("✓ get_conservative_validation_config tests passed")


def test_visibility_config():
    """Test get_visibility_config function."""
    print("Testing get_visibility_config...")
    
    # Test with complete config
    config = {
        'visibility': {
            'tau_base_m': 3.0,
            'tau_scale_per_m': 0.2,
            'sphere_radius_m': 0.25,
            'treat_no_depth_as_visible': False
        }
    }
    
    vis_config = get_visibility_config(config)
    assert vis_config['tau_base_m'] == 3.0
    assert vis_config['tau_scale_per_m'] == 0.2
    assert vis_config['sphere_radius_m'] == 0.25
    assert vis_config['treat_no_depth_as_visible'] == False
    
    # Test with missing section (should use defaults)
    empty_config = {}
    vis_config = get_visibility_config(empty_config)
    assert vis_config['tau_base_m'] == 2.0
    assert vis_config['tau_scale_per_m'] == 0.1
    assert vis_config['sphere_radius_m'] == 0.15
    assert vis_config['treat_no_depth_as_visible'] == True
    
    print("✓ get_visibility_config tests passed")


def test_camera_config():
    """Test get_camera_config function with overrides."""
    print("Testing get_camera_config...")
    
    config = {
        'visibility': {
            'tau_base_m': 2.0,
            'tau_scale_per_m': 0.1
        },
        'visibility_overrides': {
            '120_back': {
                'tau_base_m': 0.5,
                'sphere_radius_m': 0.22
            }
        }
    }
    
    # Test camera with overrides
    back_config = get_camera_config(config, '120_back')
    assert back_config['tau_base_m'] == 0.5  # Override
    assert back_config['sphere_radius_m'] == 0.22  # Override
    assert back_config['tau_scale_per_m'] == 0.1  # Base config
    
    # Test camera without overrides
    front_config = get_camera_config(config, '60_front')
    assert front_config['tau_base_m'] == 2.0  # Base config
    assert front_config['tau_scale_per_m'] == 0.1  # Base config
    
    print("✓ get_camera_config tests passed")


def test_configuration_startup_validation():
    """Test validate_configuration_startup function."""
    print("Testing validate_configuration_startup...")
    
    # Test valid configuration
    valid_config = {
        'conservative_validation': {
            'enabled': True,
            'azimuth_tolerance_deg': 10.0,
            'min_depth_points': 5,
            'tau_multiplier': 1.5
        },
        'visibility': {
            'tau_base_m': 2.0,
            'treat_no_depth_as_visible': True
        },
        'fov_filter': {
            'enabled': True,
            'soft_margin_deg': 10.0
        }
    }
    
    is_valid, critical_errors, warnings = validate_configuration_startup(valid_config)
    assert is_valid == True
    assert len(critical_errors) == 0
    
    # Test configuration with warnings
    warning_config = {
        'conservative_validation': {
            'enabled': True,
            'azimuth_tolerance_deg': 35.0,  # Large tolerance (warning)
            'min_depth_points': 2,  # Low points (warning)
            'tau_multiplier': 4.0  # High multiplier (warning)
        },
        'visibility': {
            'tau_base_m': 6.0,  # Large tau_base (warning)
            'treat_no_depth_as_visible': False  # Conflict with conservative validation (warning)
        }
    }
    
    is_valid, critical_errors, warnings = validate_configuration_startup(warning_config)
    assert is_valid == True  # Should still be valid
    assert len(warnings) > 0  # Should have warnings
    
    print("✓ validate_configuration_startup tests passed")


def test_graceful_degradation():
    """Test graceful degradation when configuration is invalid."""
    print("Testing graceful degradation...")
    
    # Test with completely invalid config
    invalid_config = "not_a_dict"
    
    try:
        safe_config = handle_configuration_degradation({}, Exception("Test error"))
        assert isinstance(safe_config, dict)
        assert 'visibility' in safe_config
        assert 'conservative_validation' in safe_config
        assert safe_config['conservative_validation']['enabled'] == False  # Should be disabled for safety
        print("✓ Graceful degradation handled invalid config")
    except Exception as e:
        print(f"✗ Graceful degradation failed: {e}")
        return False
    
    # Test minimal config creation
    minimal_config = create_minimal_config()
    assert isinstance(minimal_config, dict)
    assert 'visibility' in minimal_config
    assert 'conservative_validation' in minimal_config
    assert 'fov_filter' in minimal_config
    print("✓ Minimal config creation works")
    
    return True


def test_config_loader_integration():
    """Test integration with config loader."""
    print("Testing config loader integration...")
    
    # Create a temporary config file
    test_config = {
        'conservative_validation': {
            'enabled': True,
            'azimuth_tolerance_deg': 12.0
        },
        'visibility': {
            'tau_base_m': 2.5,
            'treat_no_depth_as_visible': True
        }
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(test_config, f)
        temp_config_path = f.name
    
    try:
        # Test safe loading
        loaded_config = load_config_safe(temp_config_path)
        assert isinstance(loaded_config, dict)
        assert 'conservative_validation' in loaded_config
        assert 'visibility' in loaded_config
        
        # Test that safe access works with loaded config
        cv_config = get_conservative_validation_config(loaded_config)
        assert cv_config['enabled'] == True
        assert cv_config['azimuth_tolerance_deg'] == 12.0
        
        print("✓ Config loader integration works")
        
    finally:
        # Clean up
        os.unlink(temp_config_path)
    
    # Test loading non-existent file (should use minimal config)
    safe_config = load_config_safe("non_existent_file.yaml")
    assert isinstance(safe_config, dict)
    assert 'conservative_validation' in safe_config
    print("✓ Safe loading of non-existent file works")


def test_logging_and_summary():
    """Test logging and configuration summary functions."""
    print("Testing logging and summary functions...")
    
    config = {
        'conservative_validation': {
            'enabled': True,
            'azimuth_tolerance_deg': 10.0,
            'min_depth_points': 5,
            'tau_multiplier': 1.5
        },
        'visibility': {
            'tau_base_m': 2.0,
            'treat_no_depth_as_visible': True
        },
        'fov_filter': {
            'enabled': True,
            'soft_margin_deg': 10.0
        }
    }
    
    try:
        # This should not raise an exception
        log_configuration_access_summary(config)
        print("✓ Configuration summary logging works")
    except Exception as e:
        print(f"✗ Configuration summary logging failed: {e}")
        return False
    
    return True


def main():
    """Run all tests."""
    print("Running safe configuration access tests...\n")
    
    tests = [
        test_safe_config_value,
        test_conservative_validation_config,
        test_visibility_config,
        test_camera_config,
        test_configuration_startup_validation,
        test_graceful_degradation,
        test_config_loader_integration,
        test_logging_and_summary
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            result = test()
            if result is not False:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("✓ All safe configuration access tests passed!")
        return True
    else:
        print("✗ Some tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)