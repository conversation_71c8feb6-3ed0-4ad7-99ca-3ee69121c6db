"""
Conservative validation module for visibility calculation.

This module provides conservative validation functions that prioritize visibility
unless there is overwhelming evidence of occlusion. It handles sparse depth data
and calibration inaccuracies by using generous tolerances and fallback strategies.
"""

import numpy as np
import logging
from typing import Tuple, Dict, Any, Optional

logger = logging.getLogger(__name__)


def is_in_camera_fov_loose(object_center: np.ndarray, 
                          camera, 
                          tolerance_deg: float = 10.0) -> bool:
    """
    Check if object is within camera FOV with generous tolerance.
    
    This function expands the nominal camera FOV by a configurable tolerance
    to include edge cases that might be missed by strict FOV gating.
    
    Args:
        object_center: 3D object center coordinates (x, y, z) in base frame
        camera: Camera model with transformation matrix T_base_cam
        tolerance_deg: Additional degrees beyond nominal FOV (default: 10.0)
        
    Returns:
        True if object is within expanded FOV, False otherwise
    """
    try:
        # Transform object center to camera frame
        P_h = np.array([object_center[0], object_center[1], object_center[2], 1.0], dtype=float)
        Pc = (camera.T_base_cam @ P_h)[:3]
        Xc, Yc, Zc = Pc
        
        # Check if object is behind camera (assuming z-forward positive)
        if Zc <= 0.0:
            logger.debug(f"Object behind camera: Zc={Zc:.3f}")
            return False
            
        # Calculate azimuth and elevation angles
        theta = np.arctan2(Xc, Zc)  # Horizontal angle (azimuth)
        phi = np.arctan2(Yc, Zc)    # Vertical angle (elevation)
        
        # Convert tolerance to radians
        tolerance_rad = np.deg2rad(tolerance_deg)
        
        # Use generous FOV limits - assume reasonable defaults if not available
        # These are conservative estimates for typical automotive cameras
        default_h_fov = np.deg2rad(120.0)  # 120 degrees horizontal
        default_v_fov = np.deg2rad(50.0)   # 50 degrees vertical
        
        # Expand FOV by tolerance
        h_half = default_h_fov * 0.5 + tolerance_rad
        v_half = default_v_fov * 0.5 + tolerance_rad
        
        # Check if within expanded FOV
        in_h_fov = abs(theta) <= h_half
        in_v_fov = abs(phi) <= v_half
        
        result = in_h_fov and in_v_fov
        
        logger.debug(f"FOV check: theta={np.rad2deg(theta):.1f}°, phi={np.rad2deg(phi):.1f}°, "
                    f"h_limit={np.rad2deg(h_half):.1f}°, v_limit={np.rad2deg(v_half):.1f}°, "
                    f"result={result}")
        
        return result
        
    except Exception as e:
        logger.warning(f"Error in loose FOV check: {e}")
        # Conservative fallback: assume visible if check fails
        return True


def conservative_depth_validation(box3d_center: np.ndarray,
                                box3d_dims: np.ndarray,
                                box3d_yaw: float,
                                camera,
                                depth_map: np.ndarray,
                                config: Dict[str, Any]) -> float:
    """
    Perform conservative depth-based visibility validation.
    
    This function analyzes depth data around the object's projection with
    conservative assumptions that prioritize visibility when data is sparse
    or ambiguous.
    
    Args:
        box3d_center: 3D bounding box center (x, y, z)
        box3d_dims: Box dimensions (length, width, height)
        box3d_yaw: Box rotation around z-axis
        camera: Camera model for projection
        depth_map: Dense depth map from point cloud (H, W) with np.inf for missing
        config: Configuration parameters
        
    Returns:
        Visibility score between 0.0 and 1.0
    """
    try:
        # Import safe access utilities
        from ..config.safe_access import get_safe_config_value, get_conservative_validation_config, get_visibility_config
        
        # Extract configuration parameters with safe defaults
        conservative_config = get_conservative_validation_config(config)
        min_depth_points = conservative_config['min_depth_points']
        tau_multiplier = conservative_config['tau_multiplier']
        
        # Get base visibility parameters
        visibility_config = get_visibility_config(config)
        tau_base_m = visibility_config['tau_base_m'] * tau_multiplier
        tau_scale_per_m = visibility_config['tau_scale_per_m'] * tau_multiplier
        
        # Generate sample points on object surface
        from ..geometry.box3d import sample_box_surface
        
        l, w, h = box3d_dims
        surface_points = sample_box_surface(
            center=box3d_center,
            l=l, w=w, h=h,
            yaw=box3d_yaw,
            samples_per_face=100  # Conservative sampling
        )
        
        if len(surface_points) == 0:
            logger.warning("No surface points generated for object")
            return 1.0  # Conservative: assume visible
            
        # Project surface points to image
        uvz = camera.project(surface_points)
        u = np.round(uvz[:, 0]).astype(int)
        v = np.round(uvz[:, 1]).astype(int)
        z_obj = uvz[:, 2]
        
        # Filter valid projections
        H, W = depth_map.shape
        valid_mask = (
            np.isfinite(z_obj) & 
            (z_obj > 0) & 
            (u >= 0) & (u < W) & 
            (v >= 0) & (v < H)
        )
        
        if not np.any(valid_mask):
            logger.debug("No valid projections for object")
            return 1.0  # Conservative: assume visible
            
        u_valid = u[valid_mask]
        v_valid = v[valid_mask]
        z_obj_valid = z_obj[valid_mask]
        
        # Sample depth values at projected locations
        depth_values = depth_map[v_valid, u_valid]
        finite_depth_mask = np.isfinite(depth_values)
        
        # Check if we have sufficient depth data
        num_finite_depths = np.sum(finite_depth_mask)
        if num_finite_depths < min_depth_points:
            logger.debug(f"Insufficient depth data: {num_finite_depths} < {min_depth_points}, assuming visible")
            return 1.0  # Conservative: assume visible when depth data is sparse
            
        # Conservative occlusion analysis
        depth_finite = depth_values[finite_depth_mask]
        z_obj_finite = z_obj_valid[finite_depth_mask]
        
        # Calculate adaptive tolerance based on distance
        distances = z_obj_finite
        tolerances = tau_base_m + tau_scale_per_m * distances
        
        # Count occluded points (depth significantly less than object depth)
        occluded_mask = depth_finite < (z_obj_finite - tolerances)
        num_occluded = np.sum(occluded_mask)
        total_points = len(z_obj_finite)
        
        # Calculate visibility score
        visibility = 1.0 - (num_occluded / total_points)
        visibility = max(0.0, min(1.0, visibility))  # Clamp to [0, 1]
        
        logger.debug(f"Conservative depth validation: {num_occluded}/{total_points} occluded, "
                    f"visibility={visibility:.3f}")
        
        return visibility
        
    except Exception as e:
        logger.warning(f"Error in conservative depth validation: {e}")
        # Conservative fallback: assume visible if validation fails
        return 1.0


def get_conservative_config_safe(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Safely extract conservative validation configuration with defaults.
    
    Args:
        config: Full configuration dictionary
        
    Returns:
        Conservative validation configuration with safe defaults
    """
    try:
        # Use the centralized safe access utility
        from ..config.safe_access import get_conservative_validation_config
        return get_conservative_validation_config(config)
    except ImportError:
        # Fallback for backward compatibility
        conservative_config = config.get('conservative_validation', {})
        
        defaults = {
            'enabled': False,
            'azimuth_tolerance_deg': 10.0,
            'min_depth_points': 5,
            'tau_multiplier': 1.5
        }
        
        # Merge with defaults
        safe_config = defaults.copy()
        safe_config.update(conservative_config)
        
        # Validate parameters
        safe_config['azimuth_tolerance_deg'] = max(0.0, min(45.0, safe_config['azimuth_tolerance_deg']))
        safe_config['min_depth_points'] = max(1, safe_config['min_depth_points'])
        safe_config['tau_multiplier'] = max(1.0, min(5.0, safe_config['tau_multiplier']))
        
        return safe_config


def validate_conservative_config(config: Dict[str, Any]) -> Tuple[bool, str]:
    """
    Validate conservative validation configuration parameters using safe access patterns.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Use safe access utilities for validation
        from ..config.safe_access import get_safe_config_value
        
        # Check if conservative_validation section exists
        if 'conservative_validation' not in config:
            return True, "conservative_validation section not found (optional)"
        
        conservative_config = config.get('conservative_validation', {})
        
        if not isinstance(conservative_config, dict):
            return False, "conservative_validation must be a dictionary"
            
        # Check enabled flag with safe access
        enabled = get_safe_config_value(config, 'conservative_validation.enabled', False, bool)
        
        # Check tolerance with safe access and validation
        tolerance = get_safe_config_value(config, 'conservative_validation.azimuth_tolerance_deg', 10.0, (int, float))
        if tolerance < 0 or tolerance > 45:
            return False, "azimuth_tolerance_deg must be between 0 and 45 degrees"
            
        # Check minimum depth points with safe access
        min_points = get_safe_config_value(config, 'conservative_validation.min_depth_points', 5, int)
        if min_points < 1:
            return False, "min_depth_points must be a positive integer"
            
        # Check tau multiplier with safe access
        tau_mult = get_safe_config_value(config, 'conservative_validation.tau_multiplier', 1.5, (int, float))
        if tau_mult < 0.5 or tau_mult > 5.0:
            return False, "tau_multiplier must be between 0.5 and 5.0"
            
        return True, "Configuration valid"
        
    except Exception as e:
        return False, f"Configuration validation error: {e}"