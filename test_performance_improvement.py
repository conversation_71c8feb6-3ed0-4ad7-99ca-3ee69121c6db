#!/usr/bin/env python3
"""
Performance test script to demonstrate the efficiency gains from category filtering.

This script simulates the processing pipeline with and without category filtering
to show the performance improvement achieved by reducing the number of objects
that need to go through the expensive visibility calculation pipeline.
"""

import json
import time
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from robobus_vis.pipeline.run_batch import filter_objects_by_category


def simulate_visibility_processing(objects, processing_time_per_object=0.01):
    """
    Simulate the expensive visibility calculation processing.
    
    Args:
        objects: List of objects to process
        processing_time_per_object: Simulated processing time per object in seconds
    
    Returns:
        Total processing time
    """
    start_time = time.time()
    
    # Simulate processing each object
    for i, obj in enumerate(objects):
        # Simulate some computation
        time.sleep(processing_time_per_object)
        
        # Show progress for longer runs
        if len(objects) > 50 and (i + 1) % 20 == 0:
            print(f"  Processed {i + 1}/{len(objects)} objects...")
    
    end_time = time.time()
    return end_time - start_time


def run_performance_test():
    """Run performance comparison test."""
    
    print("Performance Improvement Test")
    print("="*60)
    
    # Load sample data
    sample_json_path = Path("visibility_demo_data/clip_dataset_1/result_json/1733374539.701036214.json")
    
    if not sample_json_path.exists():
        print(f"Error: Sample JSON file not found: {sample_json_path}")
        return False
    
    print(f"Loading sample data from: {sample_json_path}")
    
    with open(sample_json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    objects = data.get('result', {}).get('data', [])
    print(f"Loaded {len(objects)} objects from sample data")
    
    # Apply category filtering
    filtered_objects, filter_stats = filter_objects_by_category(objects)
    
    print(f"\nCategory filtering results:")
    print(f"  Original objects: {filter_stats['total_objects']}")
    print(f"  Filtered objects: {filter_stats['filtered_objects']}")
    print(f"  Reduction: {filter_stats['filter_efficiency']:.1%}")
    
    # Simulate processing time per object (realistic estimate based on project analysis)
    # Based on the technical report: single object single camera takes 2-5ms
    # With 7 cameras, that's roughly 14-35ms per object
    processing_time_per_object = 0.025  # 25ms per object (conservative estimate)
    
    print(f"\nSimulating visibility processing...")
    print(f"  Estimated processing time per object: {processing_time_per_object*1000:.1f}ms")
    
    # Test 1: Process all objects (without filtering)
    print(f"\n" + "-"*40)
    print(f"Test 1: Processing ALL objects ({len(objects)} objects)")
    start_time = time.time()
    time_without_filter = simulate_visibility_processing(objects, processing_time_per_object)
    print(f"  Total processing time: {time_without_filter:.2f} seconds")
    
    # Test 2: Process only filtered objects (with filtering)
    print(f"\n" + "-"*40)
    print(f"Test 2: Processing FILTERED objects ({len(filtered_objects)} objects)")
    start_time = time.time()
    time_with_filter = simulate_visibility_processing(filtered_objects, processing_time_per_object)
    print(f"  Total processing time: {time_with_filter:.2f} seconds")
    
    # Calculate performance improvement
    time_saved = time_without_filter - time_with_filter
    speedup_factor = time_without_filter / time_with_filter if time_with_filter > 0 else float('inf')
    efficiency_improvement = (time_saved / time_without_filter) * 100 if time_without_filter > 0 else 0
    
    print(f"\n" + "="*60)
    print(f"PERFORMANCE IMPROVEMENT RESULTS")
    print(f"="*60)
    print(f"Processing time without filtering: {time_without_filter:.2f} seconds")
    print(f"Processing time with filtering:    {time_with_filter:.2f} seconds")
    print(f"Time saved:                        {time_saved:.2f} seconds")
    print(f"Speedup factor:                    {speedup_factor:.2f}x")
    print(f"Efficiency improvement:            {efficiency_improvement:.1f}%")
    
    # Extrapolate to larger datasets
    print(f"\n" + "-"*60)
    print(f"EXTRAPOLATION TO LARGER DATASETS")
    print(f"-"*60)
    
    dataset_sizes = [1000, 5000, 10000, 50000]  # Number of frames
    objects_per_frame = len(objects)
    
    for dataset_size in dataset_sizes:
        total_objects = dataset_size * objects_per_frame
        filtered_objects_total = dataset_size * len(filtered_objects)
        
        estimated_time_without = (total_objects * processing_time_per_object) / 60  # minutes
        estimated_time_with = (filtered_objects_total * processing_time_per_object) / 60  # minutes
        estimated_time_saved = estimated_time_without - estimated_time_with
        
        print(f"Dataset: {dataset_size:,} frames ({total_objects:,} objects)")
        print(f"  Without filtering: {estimated_time_without:.1f} minutes")
        print(f"  With filtering:    {estimated_time_with:.1f} minutes")
        print(f"  Time saved:        {estimated_time_saved:.1f} minutes ({estimated_time_saved/60:.1f} hours)")
        print()
    
    # Real-world impact analysis
    print(f"REAL-WORLD IMPACT ANALYSIS")
    print(f"-"*60)
    print(f"Category filtering provides significant performance benefits:")
    print(f"• Reduces object processing load by {filter_stats['filter_efficiency']:.1%}")
    print(f"• Achieves {speedup_factor:.2f}x speedup in visibility calculations")
    print(f"• Saves {efficiency_improvement:.1f}% of total processing time")
    print(f"• Enables processing of larger datasets in the same time")
    print(f"• Reduces computational resource requirements")
    print(f"• Improves overall pipeline throughput")
    
    return True


def analyze_category_distribution():
    """Analyze the distribution of categories in the sample data."""
    
    print(f"\n" + "="*60)
    print(f"CATEGORY DISTRIBUTION ANALYSIS")
    print(f"="*60)
    
    # Load sample data
    sample_json_path = Path("visibility_demo_data/clip_dataset_1/result_json/1733374539.701036214.json")
    
    with open(sample_json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    objects = data.get('result', {}).get('data', [])
    
    # Count categories
    category_counts = {}
    for obj in objects:
        label = str(obj.get('label', 'unknown'))
        category_counts[label] = category_counts.get(label, 0) + 1
    
    # Define category meanings (based on autonomous driving standards)
    category_meanings = {
        '1000': 'Pedestrian',
        '2000': 'Cyclist', 
        '3000': 'Car',
        '4000': 'Truck',
        '5000': 'Bus',
        '6000': 'Motorcycle',
        '7000': 'Traffic Sign',
        '8000': 'Traffic Light',
        '9000': 'Other Vehicle',
        '10000': 'Unknown/Other'
    }
    
    total_objects = len(objects)
    allowed_categories = ['1000', '2000', '3000', '4000', '5000', '6000', '7000']
    
    print(f"Total objects in sample: {total_objects}")
    print(f"\nCategory breakdown:")
    
    kept_objects = 0
    filtered_objects = 0
    
    for category in sorted(category_counts.keys()):
        count = category_counts[category]
        percentage = (count / total_objects) * 100
        meaning = category_meanings.get(category, 'Unknown')
        
        if category in allowed_categories:
            status = "KEPT"
            kept_objects += count
        else:
            status = "FILTERED"
            filtered_objects += count
        
        print(f"  {category} ({meaning}): {count:3d} objects ({percentage:5.1f}%) [{status}]")
    
    print(f"\nFiltering summary:")
    print(f"  Objects kept:     {kept_objects:3d} ({(kept_objects/total_objects)*100:.1f}%)")
    print(f"  Objects filtered: {filtered_objects:3d} ({(filtered_objects/total_objects)*100:.1f}%)")
    
    print(f"\nFiltering rationale:")
    print(f"• Focuses on core autonomous driving objects (vehicles, pedestrians, cyclists)")
    print(f"• Excludes infrastructure objects (traffic lights, signs) that may not need")
    print(f"  detailed visibility analysis for perception tasks")
    print(f"• Reduces processing load while maintaining focus on safety-critical objects")


if __name__ == "__main__":
    try:
        # Run performance test
        success = run_performance_test()
        
        if success:
            # Run category analysis
            analyze_category_distribution()
            
            print(f"\n🎉 Performance test completed successfully!")
            print(f"Category filtering demonstrates significant efficiency improvements.")
        else:
            print(f"\n❌ Performance test failed.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test execution failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
